using Microsoft.EntityFrameworkCore;
using AutoClassTime.Data;
using AutoClassTime.Models;
using AutoClassTime.Services;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace AutoClassTime.ViewModels
{
    /// <summary>
    /// 排课管理ViewModel
    /// </summary>
    public class ScheduleViewModel : ViewModelBase
    {
        private readonly ClassTimeDbContext _context;
        private readonly SchedulingService _schedulingService;
        private readonly ConflictDetectionService _conflictService;

        private ObservableCollection<ClassRecord> _classRecords;
        private ObservableCollection<Student> _students;
        private ObservableCollection<Course> _courses;
        private ObservableCollection<TimeSlot> _availableTimeSlots;

        private ClassRecord _selectedClassRecord;
        private Student _selectedStudent;
        private Course _selectedCourse;
        private TimeSlot _selectedTimeSlot;
        private DateTime _selectedDate = DateTime.Today;
        private bool _isScheduling;

        public ScheduleViewModel(ClassTimeDbContext context, SchedulingService schedulingService, ConflictDetectionService conflictService)
        {
            _context = context;
            _schedulingService = schedulingService;
            _conflictService = conflictService;

            ClassRecords = new ObservableCollection<ClassRecord>();
            Students = new ObservableCollection<Student>();
            Courses = new ObservableCollection<Course>();
            AvailableTimeSlots = new ObservableCollection<TimeSlot>();

            // 初始化命令
            LoadDateCommand = new RelayCommand(async () => await LoadClassRecordsAsync());
            ScheduleClassCommand = new RelayCommand(ScheduleClass);
            AutoScheduleCommand = new RelayCommand(async () => await AutoScheduleAsync());
            CancelClassCommand = new RelayCommand(async () => await CancelClassAsync(), () => SelectedClassRecord != null && SelectedClassRecord.Status != ClassStatus.Cancelled);
            RestoreClassCommand = new RelayCommand(async () => await RestoreClassAsync(), () => SelectedClassRecord != null && SelectedClassRecord.Status == ClassStatus.Cancelled);
            RescheduleClassCommand = new RelayCommand(async () => await RescheduleClassAsync(), () => SelectedClassRecord != null && SelectedTimeSlot != null);
            RefreshCommand = new RelayCommand(async () => await LoadAllDataAsync());

            // 加载数据
            _ = LoadAllDataAsync();
        }

        #region 属性

        public ObservableCollection<ClassRecord> ClassRecords
        {
            get => _classRecords;
            set => SetProperty(ref _classRecords, value);
        }

        public ObservableCollection<Student> Students
        {
            get => _students;
            set => SetProperty(ref _students, value);
        }

        public ObservableCollection<Course> Courses
        {
            get => _courses;
            set => SetProperty(ref _courses, value);
        }

        public ObservableCollection<TimeSlot> AvailableTimeSlots
        {
            get => _availableTimeSlots;
            set => SetProperty(ref _availableTimeSlots, value);
        }

        public ClassRecord SelectedClassRecord
        {
            get => _selectedClassRecord;
            set
            {
                SetProperty(ref _selectedClassRecord, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public Student SelectedStudent
        {
            get => _selectedStudent;
            set
            {
                SetProperty(ref _selectedStudent, value);
                if (value != null)
                {
                    _ = LoadAvailableTimeSlotsAsync();
                }
            }
        }

        public Course SelectedCourse
        {
            get => _selectedCourse;
            set => SetProperty(ref _selectedCourse, value);
        }

        public TimeSlot SelectedTimeSlot
        {
            get => _selectedTimeSlot;
            set
            {
                SetProperty(ref _selectedTimeSlot, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public DateTime SelectedDate
        {
            get => _selectedDate;
            set => SetProperty(ref _selectedDate, value);
        }

        public bool IsScheduling
        {
            get => _isScheduling;
            set => SetProperty(ref _isScheduling, value);
        }

        #endregion

        #region 命令

        public ICommand LoadDateCommand { get; }
        public ICommand ScheduleClassCommand { get; }
        public ICommand AutoScheduleCommand { get; }
        public ICommand CancelClassCommand { get; }
        public ICommand RestoreClassCommand { get; }
        public ICommand RescheduleClassCommand { get; }
        public ICommand RefreshCommand { get; }

        #endregion

        #region 方法

        private async Task LoadAllDataAsync()
        {
            await LoadStudentsAsync();
            await LoadCoursesAsync();
            await LoadClassRecordsAsync();
        }

        private async Task LoadStudentsAsync()
        {
            try
            {
                var students = await _context.Students
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.Name)
                    .ToListAsync();

                Students.Clear();
                foreach (var student in students)
                {
                    Students.Add(student);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载学生数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadCoursesAsync()
        {
            try
            {
                var courses = await _context.Courses
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                Courses.Clear();
                foreach (var course in courses)
                {
                    Courses.Add(course);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载课程数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadClassRecordsAsync()
        {
            try
            {
                var classRecords = await _context.ClassRecords
                    .Include(cr => cr.Student)
                    .Include(cr => cr.SecondStudent)
                    .Include(cr => cr.Course)
                    .Include(cr => cr.TimeSlot)
                    .Where(cr => cr.TimeSlot.Date.Date == SelectedDate.Date)
                    .ToListAsync();

                // 在内存中排序，避免SQLite的TimeSpan排序问题
                // 按状态排序：已安排 > 进行中 > 已完成 > 已取消 > 学生缺席
                var sortedRecords = classRecords
                    .OrderBy(cr => cr.Status == ClassStatus.Scheduled ? 0 :
                                  cr.Status == ClassStatus.InProgress ? 1 :
                                  cr.Status == ClassStatus.Completed ? 2 :
                                  cr.Status == ClassStatus.Cancelled ? 3 : 4)
                    .ThenBy(cr => cr.TimeSlot.StartTime)
                    .ToList();

                ClassRecords.Clear();
                foreach (var record in sortedRecords)
                {
                    ClassRecords.Add(record);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载课程记录失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadAvailableTimeSlotsAsync()
        {
            try
            {
                if (SelectedStudent == null) return;

                var endDate = SelectedDate.AddDays(7); // 显示一周的可用时间槽
                var availableTimeSlots = await _conflictService.GetAvailableTimeSlotsForStudentAsync(
                    SelectedStudent.Id, SelectedDate, endDate);

                AvailableTimeSlots.Clear();
                foreach (var timeSlot in availableTimeSlots)
                {
                    AvailableTimeSlots.Add(timeSlot);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载可用时间槽失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ScheduleClass()
        {
            if (SelectedStudent == null || SelectedCourse == null || SelectedTimeSlot == null)
            {
                MessageBox.Show("请选择学生、课程和时间槽", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            IsScheduling = true;
        }

        private async Task AutoScheduleAsync()
        {
            try
            {
                if (SelectedStudent == null || SelectedCourse == null)
                {
                    MessageBox.Show("请选择学生和课程", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var request = new SchedulingRequest
                {
                    StudentId = SelectedStudent.Id,
                    CourseId = SelectedCourse.Id,
                    StartDate = SelectedDate,
                    EndDate = SelectedDate.AddDays(7)
                };

                var result = await _schedulingService.AutoScheduleAsync(request);

                if (result.Success)
                {
                    MessageBox.Show($"自动排课成功！\n时间：{result.SelectedTimeSlot.Date:yyyy-MM-dd} {result.SelectedTimeSlot.StartTime:hh\\:mm}-{result.SelectedTimeSlot.EndTime:hh\\:mm}",
                        "排课成功", MessageBoxButton.OK, MessageBoxImage.Information);

                    await LoadClassRecordsAsync();
                    await LoadAvailableTimeSlotsAsync();
                }
                else
                {
                    MessageBox.Show($"自动排课失败：{result.Message}", "排课失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"自动排课失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CancelClassAsync()
        {
            try
            {
                if (SelectedClassRecord == null) return;

                var result = MessageBox.Show($"确定要取消课程吗？\n学生：{SelectedClassRecord.Student.Name}\n课程：{SelectedClassRecord.Course.Name}\n时间：{SelectedClassRecord.TimeSlot.Date:yyyy-MM-dd} {SelectedClassRecord.TimeSlot.StartTime:hh\\:mm}-{SelectedClassRecord.TimeSlot.EndTime:hh\\:mm}",
                    "确认取消", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                var success = await _schedulingService.CancelClassAsync(SelectedClassRecord.Id, "用户手动取消");

                if (success)
                {
                    MessageBox.Show("课程取消成功", "取消成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadClassRecordsAsync();
                    await LoadAvailableTimeSlotsAsync();
                }
                else
                {
                    MessageBox.Show("课程取消失败", "取消失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"取消课程失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task RestoreClassAsync()
        {
            try
            {
                if (SelectedClassRecord == null) return;

                var result = MessageBox.Show($"确定要恢复课程吗？\n学生：{SelectedClassRecord.Student.Name}\n课程：{SelectedClassRecord.Course.Name}\n时间：{SelectedClassRecord.TimeSlot.Date:yyyy-MM-dd} {SelectedClassRecord.TimeSlot.StartTime:hh\\:mm}-{SelectedClassRecord.TimeSlot.EndTime:hh\\:mm}",
                    "确认恢复", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                var restoreResult = await _schedulingService.RestoreClassAsync(SelectedClassRecord.Id);

                if (restoreResult.Success)
                {
                    MessageBox.Show("课程恢复成功", "恢复成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadClassRecordsAsync();
                    await LoadAvailableTimeSlotsAsync();
                }
                else
                {
                    MessageBox.Show($"课程恢复失败：{restoreResult.Message}", "恢复失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"恢复课程失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task RescheduleClassAsync()
        {
            try
            {
                if (SelectedClassRecord == null || SelectedTimeSlot == null) return;

                var result = MessageBox.Show($"确定要重新安排课程时间吗？\n新时间：{SelectedTimeSlot.Date:yyyy-MM-dd} {SelectedTimeSlot.StartTime:hh\\:mm}-{SelectedTimeSlot.EndTime:hh\\:mm}",
                    "确认重新安排", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                var rescheduleResult = await _schedulingService.RescheduleClassAsync(SelectedClassRecord.Id, SelectedTimeSlot.Id);

                if (rescheduleResult.Success)
                {
                    MessageBox.Show("课程重新安排成功", "重新安排成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadClassRecordsAsync();
                    await LoadAvailableTimeSlotsAsync();
                }
                else
                {
                    MessageBox.Show($"课程重新安排失败：{rescheduleResult.Message}", "重新安排失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重新安排课程失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }
}
