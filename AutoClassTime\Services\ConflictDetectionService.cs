using Microsoft.EntityFrameworkCore;
using AutoClassTime.Data;
using AutoClassTime.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AutoClassTime.Services
{
    /// <summary>
    /// 时间冲突检测服务
    /// </summary>
    public class ConflictDetectionService
    {
        private readonly ClassTimeDbContext _context;

        public ConflictDetectionService(ClassTimeDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 检测学生时间冲突
        /// </summary>
        public async Task<ConflictResult> CheckStudentConflictAsync(int studentId, int timeSlotId, int? excludeClassRecordId = null)
        {
            try
            {
                var timeSlot = await _context.TimeSlots.FindAsync(timeSlotId);
                if (timeSlot == null)
                {
                    return new ConflictResult { HasConflict = true, Message = "时间槽不存在" };
                }

                var query = _context.ClassRecords
                    .Include(cr => cr.TimeSlot)
                    .Include(cr => cr.Course)
                    .Where(cr => (cr.StudentId == studentId || cr.SecondStudentId == studentId) &&
                                cr.Status != ClassStatus.Cancelled);

                if (excludeClassRecordId.HasValue)
                {
                    query = query.Where(cr => cr.Id != excludeClassRecordId.Value);
                }

                var existingRecords = await query.ToListAsync();

                foreach (var record in existingRecords)
                {
                    if (IsTimeOverlap(timeSlot, record.TimeSlot))
                    {
                        return new ConflictResult
                        {
                            HasConflict = true,
                            Message = $"学生在 {record.TimeSlot.Date:yyyy-MM-dd} {record.TimeSlot.StartTime:hh\\:mm}-{record.TimeSlot.EndTime:hh\\:mm} 已有课程安排：{record.Course.Name}",
                            ConflictingRecord = record
                        };
                    }
                }

                return new ConflictResult { HasConflict = false };
            }
            catch (Exception ex)
            {
                return new ConflictResult { HasConflict = true, Message = $"检测冲突时发生错误: {ex.Message}" };
            }
        }

        /// <summary>
        /// 检测时间槽是否已被占用
        /// </summary>
        public async Task<ConflictResult> CheckTimeSlotConflictAsync(int timeSlotId, int? excludeClassRecordId = null)
        {
            try
            {
                var query = _context.ClassRecords
                    .Include(cr => cr.TimeSlot)
                    .Include(cr => cr.Student)
                    .Include(cr => cr.Course)
                    .Where(cr => cr.TimeSlotId == timeSlotId && cr.Status != ClassStatus.Cancelled);

                if (excludeClassRecordId.HasValue)
                {
                    query = query.Where(cr => cr.Id != excludeClassRecordId.Value);
                }

                var existingRecord = await query.FirstOrDefaultAsync();

                if (existingRecord != null)
                {
                    return new ConflictResult
                    {
                        HasConflict = true,
                        Message = $"该时间槽已被占用：{existingRecord.Student.Name} - {existingRecord.Course.Name}",
                        ConflictingRecord = existingRecord
                    };
                }

                return new ConflictResult { HasConflict = false };
            }
            catch (Exception ex)
            {
                return new ConflictResult { HasConflict = true, Message = $"检测冲突时发生错误: {ex.Message}" };
            }
        }

        /// <summary>
        /// 检测双人课程冲突
        /// </summary>
        public async Task<ConflictResult> CheckPairClassConflictAsync(int student1Id, int student2Id, int timeSlotId, int? excludeClassRecordId = null)
        {
            // 检测第一个学生的冲突
            var student1Conflict = await CheckStudentConflictAsync(student1Id, timeSlotId, excludeClassRecordId);
            if (student1Conflict.HasConflict)
            {
                return student1Conflict;
            }

            // 检测第二个学生的冲突
            var student2Conflict = await CheckStudentConflictAsync(student2Id, timeSlotId, excludeClassRecordId);
            if (student2Conflict.HasConflict)
            {
                return student2Conflict;
            }

            // 检测时间槽冲突
            var timeSlotConflict = await CheckTimeSlotConflictAsync(timeSlotId, excludeClassRecordId);
            if (timeSlotConflict.HasConflict)
            {
                return timeSlotConflict;
            }

            return new ConflictResult { HasConflict = false };
        }

        /// <summary>
        /// 获取学生的可用时间槽
        /// </summary>
        public async Task<List<TimeSlot>> GetAvailableTimeSlotsForStudentAsync(int studentId, DateTime startDate, DateTime endDate)
        {
            try
            {
                // 获取指定日期范围内的所有时间槽
                var allTimeSlots = await _context.TimeSlots
                    .Where(ts => ts.Date >= startDate.Date && ts.Date <= endDate.Date && ts.IsAvailable)
                    .ToListAsync();

                // 在内存中排序，避免SQLite的TimeSpan排序问题
                allTimeSlots = allTimeSlots
                    .OrderBy(ts => ts.Date)
                    .ThenBy(ts => ts.StartTime)
                    .ToList();

                // 获取学生在该时间范围内的课程安排
                var studentRecords = await _context.ClassRecords
                    .Include(cr => cr.TimeSlot)
                    .Where(cr => (cr.StudentId == studentId || cr.SecondStudentId == studentId) &&
                                cr.TimeSlot.Date >= startDate.Date &&
                                cr.TimeSlot.Date <= endDate.Date &&
                                cr.Status != ClassStatus.Cancelled)
                    .ToListAsync();

                // 过滤掉有冲突的时间槽
                var availableTimeSlots = new List<TimeSlot>();

                foreach (var timeSlot in allTimeSlots)
                {
                    bool hasConflict = false;

                    foreach (var record in studentRecords)
                    {
                        if (IsTimeOverlap(timeSlot, record.TimeSlot))
                        {
                            hasConflict = true;
                            break;
                        }
                    }

                    if (!hasConflict)
                    {
                        availableTimeSlots.Add(timeSlot);
                    }
                }

                return availableTimeSlots;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取可用时间槽时发生错误: {ex.Message}");
                return new List<TimeSlot>();
            }
        }

        /// <summary>
        /// 检测两个时间槽是否重叠
        /// </summary>
        private bool IsTimeOverlap(TimeSlot timeSlot1, TimeSlot timeSlot2)
        {
            // 如果不是同一天，则不重叠
            if (timeSlot1.Date.Date != timeSlot2.Date.Date)
            {
                return false;
            }

            // 检测时间是否重叠
            var start1 = timeSlot1.StartTime;
            var end1 = timeSlot1.EndTime;
            var start2 = timeSlot2.StartTime;
            var end2 = timeSlot2.EndTime;

            return start1 < end2 && start2 < end1;
        }

        /// <summary>
        /// 批量检测冲突
        /// </summary>
        public async Task<List<ConflictResult>> BatchCheckConflictsAsync(List<ClassRecord> classRecords)
        {
            var results = new List<ConflictResult>();

            foreach (var record in classRecords)
            {
                ConflictResult result;

                if (record.IsPairClass)
                {
                    result = await CheckPairClassConflictAsync(record.StudentId, record.SecondStudentId.Value, record.TimeSlotId, record.Id);
                }
                else
                {
                    result = await CheckStudentConflictAsync(record.StudentId, record.TimeSlotId, record.Id);
                }

                if (result.HasConflict)
                {
                    result.ClassRecordId = record.Id;
                    results.Add(result);
                }
            }

            return results;
        }
    }

    /// <summary>
    /// 冲突检测结果
    /// </summary>
    public class ConflictResult
    {
        public bool HasConflict { get; set; }
        public string Message { get; set; }
        public ClassRecord ConflictingRecord { get; set; }
        public int? ClassRecordId { get; set; }
    }
}
