using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using AutoClassTime.Models;

namespace AutoClassTime.Converters
{
    /// <summary>
    /// 取消按钮可见性转换器
    /// </summary>
    public class CancelButtonVisibilityConverter : IValueConverter
    {
        public static readonly CancelButtonVisibilityConverter Instance = new CancelButtonVisibilityConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ClassStatus status)
            {
                // 只有已安排和进行中的课程才显示取消按钮
                return status == ClassStatus.Scheduled || status == ClassStatus.InProgress 
                    ? Visibility.Visible 
                    : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
