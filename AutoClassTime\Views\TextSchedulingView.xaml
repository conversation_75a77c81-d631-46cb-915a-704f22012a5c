<UserControl x:Class="AutoClassTime.Views.TextSchedulingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200">
    
    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 文本框样式 -->
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="FontFamily" Value="Consolas, 微软雅黑"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="BorderBrush" Value="#bdc3c7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <!-- 标题样式 -->
        <Style x:Key="TitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2c3e50"/>
            <Setter Property="Margin" Value="10,10,10,5"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#34495e" Padding="15">
            <StackPanel>
                <TextBlock Text="智能文本解析排课" 
                          FontSize="20" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="输入排课文本，系统自动解析并生成最优排课方案" 
                          FontSize="12" 
                          Foreground="#ecf0f1" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- 操作按钮栏 -->
        <Border Grid.Row="1" Background="#ecf0f1" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="🚀 开始智能排课" 
                       Style="{StaticResource ActionButtonStyle}"
                       Background="#27ae60" Foreground="White"
                       Command="{Binding ParseAndScheduleCommand}"/>
                
                <Button Content="📝 加载示例" 
                       Style="{StaticResource ActionButtonStyle}"
                       Background="#3498db" Foreground="White"
                       Command="{Binding LoadSampleCommand}"/>
                
                <Button Content="🗑️ 清空输入" 
                       Style="{StaticResource ActionButtonStyle}"
                       Background="#95a5a6" Foreground="White"
                       Command="{Binding ClearInputCommand}"/>
                
                <Button Content="📋 清空输出" 
                       Style="{StaticResource ActionButtonStyle}"
                       Background="#95a5a6" Foreground="White"
                       Command="{Binding ClearOutputCommand}"/>
                
                <!-- 处理状态指示 -->
                <StackPanel Orientation="Horizontal" Margin="20,0,0,0" VerticalAlignment="Center">
                    <ProgressBar Width="100" Height="20" 
                               IsIndeterminate="{Binding IsProcessing}"
                               Margin="0,0,10,0">
                        <ProgressBar.Visibility>
                            <Binding Path="IsProcessing">
                                <Binding.Converter>
                                    <BooleanToVisibilityConverter/>
                                </Binding.Converter>
                            </Binding>
                        </ProgressBar.Visibility>
                    </ProgressBar>
                    <TextBlock Text="正在处理中..." 
                             Foreground="#e67e22" FontWeight="Bold"
                             VerticalAlignment="Center">
                        <TextBlock.Visibility>
                            <Binding Path="IsProcessing">
                                <Binding.Converter>
                                    <BooleanToVisibilityConverter/>
                                </Binding.Converter>
                            </Binding>
                        </TextBlock.Visibility>
                    </TextBlock>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 主内容区 -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：输入区域 -->
            <Border Grid.Column="0" Background="White" Margin="0,0,5,0" 
                    BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="📝 输入排课文本" Style="{StaticResource TitleStyle}"/>

                    <ScrollViewer Grid.Row="1" Margin="10" VerticalScrollBarVisibility="Auto">
                        <TextBox Text="{Binding InputText, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource TextBoxStyle}"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="400"
                                VerticalAlignment="Stretch"/>
                    </ScrollViewer>

                    <Border Grid.Row="2" Background="#f8f9fa" Padding="10" Margin="10,0,10,10" CornerRadius="3">
                        <TextBlock Text="💡 提示：支持解析老师时间安排、学生课程需求、时间约束等信息" 
                                  FontSize="11" Foreground="#7f8c8d" TextWrapping="Wrap"/>
                    </Border>
                </Grid>
            </Border>

            <!-- 右侧：输出区域 -->
            <Border Grid.Column="1" Background="White" Margin="5,0,0,0" 
                    BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="📊 排课结果" Style="{StaticResource TitleStyle}"/>

                    <ScrollViewer Grid.Row="1" Margin="10" VerticalScrollBarVisibility="Auto">
                        <TextBox Text="{Binding OutputText}"
                                Style="{StaticResource TextBoxStyle}"
                                TextWrapping="Wrap"
                                IsReadOnly="True"
                                MinHeight="400"
                                Background="#f8f9fa"
                                VerticalAlignment="Stretch"/>
                    </ScrollViewer>

                    <Border Grid.Row="2" Background="#e8f5e8" Padding="10" Margin="10,0,10,10" CornerRadius="3">
                        <TextBlock Text="✅ 结果将按学生分组显示，包含日期、时间、科目和小班信息" 
                                  FontSize="11" Foreground="#27ae60" TextWrapping="Wrap"/>
                    </Border>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
