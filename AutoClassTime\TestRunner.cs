using AutoClassTime.Tests;
using System;
using System.Threading.Tasks;

namespace AutoClassTime
{
    /// <summary>
    /// 测试运行器
    /// </summary>
    public class TestRunner
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTestsAsync()
        {
            Console.WriteLine("=== 私教排课系统功能测试 ===");
            Console.WriteLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            var test = new BasicFunctionalityTest();
            
            try
            {
                await test.RunAllTestsAsync();
                Console.WriteLine();
                Console.WriteLine("=== 所有测试通过 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine();
                Console.WriteLine($"=== 测试失败: {ex.Message} ===");
                Console.WriteLine($"详细错误: {ex}");
            }
            finally
            {
                await test.CleanupTestDataAsync();
                test.Dispose();
                Console.WriteLine($"测试结束时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            }
        }
    }
}
