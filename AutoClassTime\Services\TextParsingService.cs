using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using AutoClassTime.Models;

namespace AutoClassTime.Services
{
    /// <summary>
    /// 文本解析排课服务
    /// </summary>
    public class TextParsingService
    {
        /// <summary>
        /// 解析排课文本（新模板格式）
        /// </summary>
        public ParsedScheduleData ParseScheduleText(string inputText)
        {
            var result = new ParsedScheduleData();

            try
            {
                var lines = inputText.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                    .Select(line => line.Trim())
                    .Where(line => !string.IsNullOrEmpty(line))
                    .ToList();

                string currentSection = "";

                foreach (var line in lines)
                {
                    // 检测章节
                    if (IsSectionHeader(line))
                    {
                        currentSection = line.Replace(":", "").Trim();
                        continue;
                    }

                    // 根据当前章节解析内容
                    switch (currentSection)
                    {
                        case "教师资源":
                            if (IsTeacherResourceLine(line))
                            {
                                var timeSlots = ParseTeacherResourceLine(line);
                                result.TimeSlots.AddRange(timeSlots);
                            }
                            break;

                        case "学生需求":
                            if (IsNewStudentRequirementLine(line))
                            {
                                var requirements = ParseNewStudentRequirements(line);
                                result.StudentRequirements.AddRange(requirements);
                            }
                            break;

                        case "排课规则":
                        case "任务":
                            // 这些章节暂时跳过，可以后续扩展
                            break;

                        default:
                            // 检测特殊要求（不在特定章节中）
                            if (IsSpecialRequirementLine(line))
                            {
                                var constraints = ParseSpecialRequirements(line);
                                result.StudentConstraints.AddRange(constraints);
                            }
                            break;
                    }
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"解析失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 生成排课安排（重新设计）
        /// </summary>
        public ScheduleResult GenerateSchedule(ParsedScheduleData parsedData)
        {
            var result = new ScheduleResult();

            try
            {
                var studentSchedules = new Dictionary<string, List<StudentClassInfo>>();

                // 初始化学生排课表
                foreach (var req in parsedData.StudentRequirements)
                {
                    if (!studentSchedules.ContainsKey(req.StudentName))
                    {
                        studentSchedules[req.StudentName] = new List<StudentClassInfo>();
                    }
                }

                // 第一步：处理拼班要求（最高优先级）
                ProcessGroupRequirements(parsedData, studentSchedules, result);

                // 第二步：处理剩余的个人课程需求
                ProcessRemainingRequirements(parsedData, studentSchedules, result);

                result.StudentSchedules = studentSchedules;
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"生成排课失败: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 处理拼班要求
        /// </summary>
        private void ProcessGroupRequirements(ParsedScheduleData parsedData,
            Dictionary<string, List<StudentClassInfo>> studentSchedules, ScheduleResult result)
        {
            // 找出所有拼班要求
            var groupRequirements = parsedData.StudentConstraints
                .Where(c => c.ConstraintType == "拼班要求")
                .ToList();

            foreach (var groupReq in groupRequirements)
            {
                var targetStudent = groupReq.TargetStudent;
                var subject = groupReq.TargetSubject;

                if (string.IsNullOrEmpty(targetStudent) || string.IsNullOrEmpty(subject))
                    continue;

                // 找到合适的拼班时间槽
                var groupSlot = FindGroupSlot(groupReq.StudentName, targetStudent, subject, parsedData, studentSchedules);

                if (groupSlot != null)
                {
                    // 为两个学生安排拼班课程
                    ArrangeGroupClass(groupReq.StudentName, targetStudent, groupSlot, studentSchedules);
                    groupSlot.IsUsed = true;

                    result.Warnings.Add($"✅ 成功安排 {groupReq.StudentName} 和 {targetStudent} 一起上 {subject} 课");
                }
                else
                {
                    result.Warnings.Add($"❌ 无法安排 {groupReq.StudentName} 和 {targetStudent} 一起上 {subject} 课");
                }
            }
        }

        /// <summary>
        /// 处理剩余的课程需求
        /// </summary>
        private void ProcessRemainingRequirements(ParsedScheduleData parsedData,
            Dictionary<string, List<StudentClassInfo>> studentSchedules, ScheduleResult result)
        {
            foreach (var requirement in parsedData.StudentRequirements)
            {
                // 计算已安排的该科目课程数量
                var arrangedCount = studentSchedules[requirement.StudentName]
                    .Count(c => c.Subject == requirement.Subject);

                // 安排剩余的课程
                for (int i = arrangedCount; i < requirement.ClassCount; i++)
                {
                    var availableSlots = FindAvailableSlotsWithConstraints(
                        requirement.StudentName,
                        requirement.Subject,
                        parsedData,
                        studentSchedules);

                    if (availableSlots.Any())
                    {
                        var selectedSlot = availableSlots.First();

                        var classInfo = new StudentClassInfo
                        {
                            StudentName = requirement.StudentName,
                            Subject = requirement.Subject,
                            Date = selectedSlot.Date,
                            StartTime = selectedSlot.StartTime,
                            EndTime = selectedSlot.EndTime,
                            Duration = selectedSlot.Duration,
                            IsGroupClass = false,
                            GroupStudents = new List<string> { requirement.StudentName }
                        };

                        studentSchedules[requirement.StudentName].Add(classInfo);
                        selectedSlot.IsUsed = true;

                        // 如果是多节课，添加节次标识
                        var classNumber = i + 1;
                        if (requirement.ClassCount > 1)
                        {
                            result.Warnings.Add($"✅ 成功安排 {requirement.StudentName} 的第 {classNumber} 节 {requirement.Subject}课");
                        }
                        else
                        {
                            result.Warnings.Add($"✅ 成功安排 {requirement.StudentName} 的 {requirement.Subject}课");
                        }
                    }
                    else
                    {
                        // 分析无法安排的原因
                        var reason = AnalyzeSchedulingFailure(requirement.StudentName, requirement.Subject, parsedData);
                        var classNumber = i + 1;
                        if (requirement.ClassCount > 1)
                        {
                            result.Warnings.Add($"❌ 无法安排 {requirement.StudentName} 的第 {classNumber} 节 {requirement.Subject}课：{reason}");
                        }
                        else
                        {
                            result.Warnings.Add($"❌ 无法安排 {requirement.StudentName} 的 {requirement.Subject}课：{reason}");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 查找拼班时间槽
        /// </summary>
        private ParsedTimeSlot FindGroupSlot(string student1, string student2, string subject,
            ParsedScheduleData parsedData, Dictionary<string, List<StudentClassInfo>> studentSchedules)
        {
            var availableSlots = parsedData.TimeSlots
                .Where(slot => slot.Subject == subject && !slot.IsUsed && slot.CanGroup)
                .Where(slot => IsSlotAvailableForStudent(student1, slot, parsedData, studentSchedules))
                .Where(slot => IsSlotAvailableForStudent(student2, slot, parsedData, studentSchedules))
                .ToList();

            return availableSlots.FirstOrDefault();
        }

        /// <summary>
        /// 安排拼班课程
        /// </summary>
        private void ArrangeGroupClass(string student1, string student2, ParsedTimeSlot slot,
            Dictionary<string, List<StudentClassInfo>> studentSchedules)
        {
            var groupStudents = new List<string> { student1, student2 };

            foreach (var student in groupStudents)
            {
                if (!studentSchedules.ContainsKey(student))
                {
                    studentSchedules[student] = new List<StudentClassInfo>();
                }

                var classInfo = new StudentClassInfo
                {
                    StudentName = student,
                    Subject = slot.Subject,
                    Date = slot.Date,
                    StartTime = slot.StartTime,
                    EndTime = slot.EndTime,
                    Duration = slot.Duration,
                    IsGroupClass = true,
                    GroupStudents = groupStudents
                };

                studentSchedules[student].Add(classInfo);
            }
        }

        /// <summary>
        /// 查找带约束的可用时间槽
        /// </summary>
        private List<ParsedTimeSlot> FindAvailableSlotsWithConstraints(string studentName, string subject,
            ParsedScheduleData parsedData, Dictionary<string, List<StudentClassInfo>> studentSchedules)
        {
            return parsedData.TimeSlots
                .Where(slot => slot.Subject == subject && !slot.IsUsed)
                .Where(slot => IsSlotAvailableForStudent(studentName, slot, parsedData, studentSchedules))
                .ToList();
        }

        /// <summary>
        /// 检查时间槽是否对学生可用
        /// </summary>
        private bool IsSlotAvailableForStudent(string studentName, ParsedTimeSlot slot,
            ParsedScheduleData parsedData, Dictionary<string, List<StudentClassInfo>> studentSchedules)
        {
            // 检查学生的时间约束
            var constraints = parsedData.StudentConstraints
                .Where(c => c.StudentName == studentName)
                .ToList();

            foreach (var constraint in constraints)
            {
                if (constraint.ConstraintType == "时间限制" && constraint.IsRestriction)
                {
                    // 检查"不想在周日上课"等约束
                    if (constraint.Description.Contains("不想在") && constraint.Description.Contains("上课"))
                    {
                        var dayMatch = Regex.Match(constraint.Description, @"不想在(周[一二三四五六日])上课");
                        if (dayMatch.Success)
                        {
                            var restrictedDay = dayMatch.Groups[1].Value;
                            if (slot.Date == restrictedDay)
                            {
                                return false; // 违反时间限制
                            }
                        }
                    }

                    // 检查"只能在周六上课"等约束
                    if (constraint.Description.Contains("只能在") && constraint.Description.Contains("上课"))
                    {
                        var dayMatch = Regex.Match(constraint.Description, @"只能在(周[一二三四五六日])上课");
                        if (dayMatch.Success)
                        {
                            var allowedDay = dayMatch.Groups[1].Value;
                            if (slot.Date != allowedDay)
                            {
                                return false; // 违反时间限制
                            }
                        }
                    }
                }
            }

            // 检查时间冲突
            if (studentSchedules.ContainsKey(studentName))
            {
                var existingClasses = studentSchedules[studentName];
                foreach (var existingClass in existingClasses)
                {
                    // 检查同一天是否有时间冲突
                    if (existingClass.Date == slot.Date)
                    {
                        if (TimeOverlaps(existingClass.StartTime, existingClass.EndTime, slot.StartTime, slot.EndTime))
                        {
                            return false; // 时间冲突
                        }

                        // 检查同一天同一科目
                        if (existingClass.Subject == slot.Subject)
                        {
                            return false; // 同一天不能上同一科目
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// 分析排课失败的原因
        /// </summary>
        private string AnalyzeSchedulingFailure(string studentName, string subject, ParsedScheduleData parsedData)
        {
            var allSubjectSlots = parsedData.TimeSlots.Where(s => s.Subject == subject).ToList();

            if (!allSubjectSlots.Any())
            {
                return $"没有{subject}老师的可用时间";
            }

            var constraints = parsedData.StudentConstraints
                .Where(c => c.StudentName == studentName && c.IsRestriction)
                .ToList();

            // 检查时间限制约束
            foreach (var constraint in constraints)
            {
                if (constraint.ConstraintType == "时间限制")
                {
                    if (constraint.Description.Contains("不想在") && constraint.Description.Contains("上课"))
                    {
                        var dayMatch = Regex.Match(constraint.Description, @"不想在(周[一二三四五六日])上课");
                        if (dayMatch.Success)
                        {
                            var restrictedDay = dayMatch.Groups[1].Value;

                            // 检查是否所有该科目的时间槽都在限制的日期
                            var restrictedSlots = allSubjectSlots.Where(s => s.Date == restrictedDay).ToList();

                            if (restrictedSlots.Count == allSubjectSlots.Count)
                            {
                                return $"{subject}老师仅在{restrictedDay}有空，与{studentName}的要求冲突";
                            }
                        }
                    }
                }
            }

            return "时间冲突或其他约束限制";
        }

        /// <summary>
        /// 格式化输出排课结果
        /// </summary>
        public string FormatScheduleOutput(ScheduleResult scheduleResult)
        {
            if (!scheduleResult.Success)
            {
                return $"排课失败: {scheduleResult.ErrorMessage}";
            }

            var output = new List<string>();
            output.Add("=== 自动排课结果 ===\n");

            foreach (var studentSchedule in scheduleResult.StudentSchedules.OrderBy(s => s.Key))
            {
                output.Add($"【{studentSchedule.Key}】");

                var classesByDate = studentSchedule.Value
                    .GroupBy(c => c.Date)
                    .OrderBy(g => g.Key);

                foreach (var dateGroup in classesByDate)
                {
                    var dayName = dateGroup.Key.Replace("周", "");
                    output.Add($"  {dayName}:");

                    foreach (var classInfo in dateGroup.OrderBy(c => c.StartTime))
                    {
                        var timeStr = $"{classInfo.StartTime}-{classInfo.EndTime}";
                        var groupInfo = "";

                        if (classInfo.IsGroupClass && classInfo.GroupStudents.Count > 1)
                        {
                            var otherStudents = classInfo.GroupStudents.Where(s => s != classInfo.StudentName);
                            if (otherStudents.Any())
                            {
                                groupInfo = $" (与{string.Join("、", otherStudents)}拼班)";
                            }
                        }

                        // 检查是否是多节课中的某一节
                        var studentClasses = scheduleResult.StudentSchedules[studentSchedule.Key];
                        var sameSubjectClasses = studentClasses.Where(c => c.Subject == classInfo.Subject).ToList();

                        if (sameSubjectClasses.Count > 1)
                        {
                            // 找出这是第几节课
                            var sortedClasses = sameSubjectClasses.OrderBy(c => c.Date).ThenBy(c => c.StartTime).ToList();
                            var classIndex = sortedClasses.IndexOf(classInfo) + 1;
                            output.Add($"    {timeStr}: 上 {classInfo.Subject}课 (第{classIndex}节){groupInfo}");
                        }
                        else
                        {
                            output.Add($"    {timeStr}: 上 {classInfo.Subject}课{groupInfo}");
                        }
                    }
                }
                output.Add("");
            }

            if (scheduleResult.Warnings.Any())
            {
                output.Add("=== 警告信息 ===");
                foreach (var warning in scheduleResult.Warnings)
                {
                    output.Add($"{warning}");
                }
            }

            return string.Join("\n", output);
        }

        #region 私有方法 - 新模板格式

        /// <summary>
        /// 检测是否为章节标题
        /// </summary>
        private bool IsSectionHeader(string line)
        {
            return line.EndsWith(":") &&
                   new[] { "教师资源", "学生需求", "排课规则", "任务" }.Any(s => line.StartsWith(s));
        }

        /// <summary>
        /// 检测是否为教师资源行
        /// </summary>
        private bool IsTeacherResourceLine(string line)
        {
            return line.Contains("老师:") && line.Contains("周");
        }

        /// <summary>
        /// 解析教师资源行
        /// </summary>
        private List<ParsedTimeSlot> ParseTeacherResourceLine(string line)
        {
            var slots = new List<ParsedTimeSlot>();

            try
            {
                // 解析格式：语文吴老师: 周六 10:00-12:00, 周日 13:00-15:00
                var parts = line.Split(':', 2);
                if (parts.Length != 2) return slots;

                var teacherInfo = parts[0].Trim();
                var timeInfo = parts[1].Trim();

                // 提取科目和教师姓名
                var subject = ExtractSubjectFromTeacher(teacherInfo);
                var teacher = ExtractTeacherName(teacherInfo);

                // 解析时间段（可能有多个，用逗号分隔）
                var timeSegments = timeInfo.Split(',');

                foreach (var segment in timeSegments)
                {
                    var trimmedSegment = segment.Trim();
                    var timeSlot = ParseWeekTimeSegment(trimmedSegment, subject, teacher);
                    if (timeSlot != null)
                    {
                        slots.Add(timeSlot);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析教师资源行失败: {line}, 错误: {ex.Message}");
            }

            return slots;
        }

        /// <summary>
        /// 从教师信息中提取科目
        /// </summary>
        private string ExtractSubjectFromTeacher(string teacherInfo)
        {
            var subjects = new[] { "语文", "数学", "英语", "物理", "化学", "道法" };
            return subjects.FirstOrDefault(s => teacherInfo.StartsWith(s)) ?? "未知科目";
        }

        /// <summary>
        /// 从教师信息中提取教师姓名
        /// </summary>
        private string ExtractTeacherName(string teacherInfo)
        {
            var subject = ExtractSubjectFromTeacher(teacherInfo);
            return teacherInfo.Replace(subject, "").Replace("老师", "").Trim();
        }

        /// <summary>
        /// 解析周时间段
        /// </summary>
        private ParsedTimeSlot ParseWeekTimeSegment(string segment, string subject, string teacher)
        {
            try
            {
                // 解析格式：周六 10:00-12:00 (可拼班)
                var canGroup = segment.Contains("(可拼班)");
                var cleanSegment = segment.Replace("(可拼班)", "").Trim();

                // 提取星期和时间
                var match = Regex.Match(cleanSegment, @"周([一二三四五六日])\s+(\d+:\d+)-(\d+:\d+)");
                if (!match.Success) return null;

                var dayOfWeek = match.Groups[1].Value;
                var startTime = match.Groups[2].Value;
                var endTime = match.Groups[3].Value;

                return new ParsedTimeSlot
                {
                    Subject = subject,
                    Date = ConvertDayOfWeekToDate(dayOfWeek),
                    StartTime = startTime,
                    EndTime = endTime,
                    Duration = CalculateDuration(startTime, endTime),
                    Students = new List<string> { "" }, // 空时间槽
                    CanGroup = canGroup,
                    Teacher = teacher
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析周时间段失败: {segment}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 转换星期到日期格式
        /// </summary>
        private string ConvertDayOfWeekToDate(string dayOfWeek)
        {
            var dayMap = new Dictionary<string, string>
            {
                { "一", "周一" }, { "二", "周二" }, { "三", "周三" }, { "四", "周四" },
                { "五", "周五" }, { "六", "周六" }, { "日", "周日" }
            };

            return dayMap.ContainsKey(dayOfWeek) ? dayMap[dayOfWeek] : dayOfWeek;
        }

        /// <summary>
        /// 检测是否为新格式学生需求行
        /// </summary>
        private bool IsNewStudentRequirementLine(string line)
        {
            return line.StartsWith("学生") && line.Contains("需要上") && line.Contains("节") && line.Contains("课");
        }

        /// <summary>
        /// 解析新格式学生需求
        /// </summary>
        private List<StudentRequirement> ParseNewStudentRequirements(string line)
        {
            var requirements = new List<StudentRequirement>();

            try
            {
                // 解析格式：学生大强: 需要上 1 节物理课，1 节数学课。
                var match = Regex.Match(line, @"学生(\w+):\s*需要上\s*(.+)");
                if (!match.Success) return requirements;

                var studentName = match.Groups[1].Value;
                var coursesText = match.Groups[2].Value;

                // 解析课程需求：1 节物理课，1 节数学课
                var courseMatches = Regex.Matches(coursesText, @"(\d+)\s*节(\w+)课");

                foreach (Match courseMatch in courseMatches)
                {
                    var count = int.Parse(courseMatch.Groups[1].Value);
                    var subject = courseMatch.Groups[2].Value;

                    requirements.Add(new StudentRequirement
                    {
                        StudentName = studentName,
                        Subject = subject,
                        ClassCount = count
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析学生需求失败: {line}, 错误: {ex.Message}");
            }

            return requirements;
        }

        /// <summary>
        /// 检测是否为特殊要求行
        /// </summary>
        private bool IsSpecialRequirementLine(string line)
        {
            return line.Contains("的特殊要求:") || line.Contains("特殊要求：");
        }

        /// <summary>
        /// 解析特殊要求
        /// </summary>
        private List<StudentConstraint> ParseSpecialRequirements(string line)
        {
            var constraints = new List<StudentConstraint>();

            try
            {
                // 解析格式：大强的特殊要求: 不想在周日上课。
                var match = Regex.Match(line, @"(\w+)的特殊要求[:：]\s*(.+)");
                if (!match.Success) return constraints;

                var studentName = match.Groups[1].Value;
                var requirement = match.Groups[2].Value.Trim('。', '.');

                var constraint = new StudentConstraint
                {
                    StudentName = studentName,
                    Description = requirement,
                    IsRestriction = requirement.Contains("不想") || requirement.Contains("不能") || requirement.Contains("不要"),
                    ConstraintType = DetermineConstraintType(requirement)
                };

                // 如果是拼班要求，提取目标学生和科目
                if (constraint.ConstraintType == "拼班要求")
                {
                    var groupMatch = Regex.Match(requirement, @"希望和\s*(\w+)\s*一起上(\w+)课");
                    if (groupMatch.Success)
                    {
                        constraint.TargetStudent = groupMatch.Groups[1].Value;
                        constraint.TargetSubject = groupMatch.Groups[2].Value;
                    }
                }

                constraints.Add(constraint);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析特殊要求失败: {line}, 错误: {ex.Message}");
            }

            return constraints;
        }

        /// <summary>
        /// 确定约束类型
        /// </summary>
        private string DetermineConstraintType(string requirement)
        {
            if (requirement.Contains("不想在") || requirement.Contains("不能在"))
                return "时间限制";
            if (requirement.Contains("希望和") && requirement.Contains("一起上"))
                return "拼班要求";
            if (requirement.Contains("希望在") && requirement.Contains("上课"))
                return "时间偏好";
            if (requirement.Contains("只能在"))
                return "时间限制";

            return "其他";
        }

        #endregion

        #region 私有方法 - 原格式兼容

        private bool IsSubjectLine(string line)
        {
            return line.Contains("老师") || line.EndsWith("：") ||
                   new[] { "语文", "数学", "英语", "物理", "化学", "道法" }.Any(s => line.StartsWith(s));
        }

        private string ExtractSubject(string line)
        {
            if (line.Contains("语文")) return "语文";
            if (line.Contains("数学")) return "数学";
            if (line.Contains("英语")) return "英语";
            if (line.Contains("物理")) return "物理";
            if (line.Contains("化学")) return "化学";
            if (line.Contains("道法")) return "道法";
            return line.Replace("老师", "").Replace("：", "").Trim();
        }

        private bool IsDateLine(string line)
        {
            return Regex.IsMatch(line, @"^\d+\.\d+") || line.Trim().EndsWith("号");
        }

        private string ExtractDate(string line)
        {
            var match = Regex.Match(line, @"(\d+)\.(\d+)");
            if (match.Success)
            {
                var month = match.Groups[1].Value;
                var day = match.Groups[2].Value;
                return $"{month}.{day}";
            }
            return line.Replace("号", "").Trim();
        }

        private bool IsTimeSlotLine(string line)
        {
            return Regex.IsMatch(line, @"\d+-\d+") || Regex.IsMatch(line, @"\d+\.\d+-\d+\.\d+");
        }

        private List<ParsedTimeSlot> ParseTimeSlotLine(string line, string subject, string date)
        {
            var slots = new List<ParsedTimeSlot>();

            // 解析时间段和学生
            var timeMatch = Regex.Match(line, @"(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)");
            if (timeMatch.Success)
            {
                var startTime = timeMatch.Groups[1].Value;
                var endTime = timeMatch.Groups[2].Value;

                // 提取学生姓名
                var studentsText = line.Substring(timeMatch.Index + timeMatch.Length).Trim();
                var students = ExtractStudentNames(studentsText);

                // 如果没有学生姓名，创建空时间槽
                if (!students.Any())
                {
                    students.Add(""); // 空时间槽
                }

                slots.Add(new ParsedTimeSlot
                {
                    Subject = subject,
                    Date = date,
                    StartTime = startTime,
                    EndTime = endTime,
                    Students = students,
                    Duration = CalculateDuration(startTime, endTime)
                });
            }

            return slots;
        }

        private List<string> ExtractStudentNames(string text)
        {
            var names = new List<string>();

            // 常见学生姓名模式
            var namePatterns = new[] { "起赫", "峻硕", "皓铮", "淇轩", "思涵", "许铜", "欣怡" };

            foreach (var name in namePatterns)
            {
                if (text.Contains(name))
                {
                    names.Add(name);
                }
            }

            return names.Distinct().ToList();
        }

        private double CalculateDuration(string startTime, string endTime)
        {
            try
            {
                var start = ParseTime(startTime);
                var end = ParseTime(endTime);
                return (end - start).TotalHours;
            }
            catch
            {
                return 2.0; // 默认2小时
            }
        }

        private TimeSpan ParseTime(string timeStr)
        {
            var parts = timeStr.Split('.');
            var hour = int.Parse(parts[0]);
            var minute = parts.Length > 1 ? int.Parse(parts[1]) : 0;
            return new TimeSpan(hour, minute, 0);
        }

        private bool IsStudentRequirementLine(string line)
        {
            return line.Contains("上") && line.Contains("节") &&
                   new[] { "语文", "数学", "英语", "物理", "化学", "道法" }.Any(s => line.Contains(s));
        }

        private List<StudentRequirement> ParseStudentRequirements(string line)
        {
            var requirements = new List<StudentRequirement>();

            // 解析学生课程需求
            var studentName = ExtractStudentNames(line).FirstOrDefault();
            if (!string.IsNullOrEmpty(studentName))
            {
                var subjects = new[] { "语文", "数学", "英语", "物理", "化学", "道法" };

                foreach (var subject in subjects)
                {
                    var pattern = $@"(\d+)节{subject}";
                    var match = Regex.Match(line, pattern);
                    if (match.Success)
                    {
                        var count = int.Parse(match.Groups[1].Value);
                        requirements.Add(new StudentRequirement
                        {
                            StudentName = studentName,
                            Subject = subject,
                            ClassCount = count
                        });
                    }
                }
            }

            return requirements;
        }

        private bool IsStudentConstraintLine(string line)
        {
            return line.Contains("不可排课") || line.Contains("可排课");
        }

        private List<StudentConstraint> ParseStudentConstraints(string line)
        {
            var constraints = new List<StudentConstraint>();

            var studentName = ExtractStudentNames(line).FirstOrDefault();
            if (!string.IsNullOrEmpty(studentName))
            {
                constraints.Add(new StudentConstraint
                {
                    StudentName = studentName,
                    Description = line,
                    IsRestriction = line.Contains("不可排课")
                });
            }

            return constraints;
        }

        private List<ParsedTimeSlot> FindAvailableSlots(string studentName, string subject,
            ParsedScheduleData parsedData, Dictionary<string, List<StudentClassInfo>> existingSchedules)
        {
            return parsedData.TimeSlots
                .Where(slot => slot.Subject == subject && !slot.IsUsed)
                .Where(slot => !HasTimeConflict(studentName, slot, existingSchedules))
                .Where(slot => !HasSameDaySubjectConflict(studentName, subject, slot, existingSchedules))
                .ToList();
        }

        private bool HasTimeConflict(string studentName, ParsedTimeSlot slot,
            Dictionary<string, List<StudentClassInfo>> existingSchedules)
        {
            if (!existingSchedules.ContainsKey(studentName))
                return false;

            return existingSchedules[studentName].Any(existing =>
                existing.Date == slot.Date &&
                TimeOverlaps(existing.StartTime, existing.EndTime, slot.StartTime, slot.EndTime));
        }

        private bool HasSameDaySubjectConflict(string studentName, string subject, ParsedTimeSlot slot,
            Dictionary<string, List<StudentClassInfo>> existingSchedules)
        {
            if (!existingSchedules.ContainsKey(studentName))
                return false;

            return existingSchedules[studentName].Any(existing =>
                existing.Date == slot.Date && existing.Subject == subject);
        }

        private bool TimeOverlaps(string start1, string end1, string start2, string end2)
        {
            var s1 = ParseTime(start1);
            var e1 = ParseTime(end1);
            var s2 = ParseTime(start2);
            var e2 = ParseTime(end2);

            return s1 < e2 && s2 < e1;
        }

        private ParsedTimeSlot SelectBestSlot(List<ParsedTimeSlot> availableSlots, StudentRequirement requirement)
        {
            // 优先选择已有学生的时间槽（小班课）
            var groupSlots = availableSlots.Where(s => s.Students.Any(st => !string.IsNullOrEmpty(st))).ToList();
            if (groupSlots.Any())
            {
                return groupSlots.First();
            }

            // 否则选择空时间槽
            return availableSlots.First();
        }

        #endregion
    }
}
