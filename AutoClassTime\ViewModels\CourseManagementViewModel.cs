using Microsoft.EntityFrameworkCore;
using AutoClassTime.Data;
using AutoClassTime.Models;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace AutoClassTime.ViewModels
{
    /// <summary>
    /// 课程管理ViewModel
    /// </summary>
    public class CourseManagementViewModel : ViewModelBase
    {
        private readonly ClassTimeDbContext _context;
        private ObservableCollection<Course> _courses;
        private Course _selectedCourse;
        private Course _editingCourse;
        private string _searchText;
        private bool _isEditing;

        public CourseManagementViewModel(ClassTimeDbContext context)
        {
            _context = context;
            Courses = new ObservableCollection<Course>();

            // 初始化命令
            AddCourseCommand = new RelayCommand(AddCourse);
            EditCourseCommand = new RelayCommand(EditCourse, () => SelectedCourse != null);
            DeleteCourseCommand = new RelayCommand(async () => await DeleteCourseAsync(), () => SelectedCourse != null);
            SaveCourseCommand = new RelayCommand(async () => await SaveCourseAsync(), () => IsEditing);
            CancelEditCommand = new RelayCommand(CancelEdit, () => IsEditing);
            SearchCommand = new RelayCommand(async () => await SearchCoursesAsync());
            RefreshCommand = new RelayCommand(async () => await LoadCoursesAsync());

            // 加载课程数据
            _ = LoadCoursesAsync();
        }

        #region 属性

        public ObservableCollection<Course> Courses
        {
            get => _courses;
            set => SetProperty(ref _courses, value);
        }

        public Course SelectedCourse
        {
            get => _selectedCourse;
            set
            {
                SetProperty(ref _selectedCourse, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public Course EditingCourse
        {
            get => _editingCourse;
            set => SetProperty(ref _editingCourse, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set
            {
                SetProperty(ref _isEditing, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        #endregion

        #region 命令

        public ICommand AddCourseCommand { get; }
        public ICommand EditCourseCommand { get; }
        public ICommand DeleteCourseCommand { get; }
        public ICommand SaveCourseCommand { get; }
        public ICommand CancelEditCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }

        #endregion

        #region 方法

        private async Task LoadCoursesAsync()
        {
            try
            {
                var courses = await _context.Courses
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                Courses.Clear();
                foreach (var course in courses)
                {
                    Courses.Add(course);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载课程数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task SearchCoursesAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    await LoadCoursesAsync();
                    return;
                }

                var courses = await _context.Courses
                    .Where(c => c.IsActive && c.Name.Contains(SearchText))
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                Courses.Clear();
                foreach (var course in courses)
                {
                    Courses.Add(course);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索课程失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddCourse()
        {
            EditingCourse = new Course
            {
                DurationMinutes = 60,
                Price = 100,
                Color = "#3498db"
            };
            IsEditing = true;
        }

        private void EditCourse()
        {
            if (SelectedCourse == null) return;

            EditingCourse = new Course
            {
                Id = SelectedCourse.Id,
                Name = SelectedCourse.Name,
                Description = SelectedCourse.Description,
                DurationMinutes = SelectedCourse.DurationMinutes,
                Price = SelectedCourse.Price,
                Color = SelectedCourse.Color,
                SupportsPairClass = SelectedCourse.SupportsPairClass,
                IsActive = SelectedCourse.IsActive,
                CreatedAt = SelectedCourse.CreatedAt,
                UpdatedAt = SelectedCourse.UpdatedAt
            };
            IsEditing = true;
        }

        private async Task SaveCourseAsync()
        {
            try
            {
                if (EditingCourse == null) return;

                // 验证必填字段
                if (string.IsNullOrWhiteSpace(EditingCourse.Name))
                {
                    MessageBox.Show("课程名称不能为空", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (EditingCourse.DurationMinutes <= 0)
                {
                    MessageBox.Show("课程时长必须大于0", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (EditingCourse.Id == 0)
                {
                    // 新增课程
                    EditingCourse.CreatedAt = DateTime.Now;
                    EditingCourse.UpdatedAt = DateTime.Now;
                    _context.Courses.Add(EditingCourse);
                }
                else
                {
                    // 更新课程
                    var existingCourse = await _context.Courses.FindAsync(EditingCourse.Id);
                    if (existingCourse != null)
                    {
                        existingCourse.Name = EditingCourse.Name;
                        existingCourse.Description = EditingCourse.Description;
                        existingCourse.DurationMinutes = EditingCourse.DurationMinutes;
                        existingCourse.Price = EditingCourse.Price;
                        existingCourse.Color = EditingCourse.Color;
                        existingCourse.SupportsPairClass = EditingCourse.SupportsPairClass;
                        existingCourse.UpdatedAt = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();
                await LoadCoursesAsync();

                IsEditing = false;
                EditingCourse = null;

                MessageBox.Show("课程信息保存成功", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存课程信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelEdit()
        {
            IsEditing = false;
            EditingCourse = null;
        }

        private async Task DeleteCourseAsync()
        {
            try
            {
                if (SelectedCourse == null) return;

                var result = MessageBox.Show($"确定要删除课程 '{SelectedCourse.Name}' 吗？\n注意：这将同时删除该课程的所有排课记录。",
                    "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                // 检查是否有未完成的课程
                var hasActiveClasses = await _context.ClassRecords
                    .AnyAsync(cr => cr.CourseId == SelectedCourse.Id && cr.Status == ClassStatus.Scheduled);

                if (hasActiveClasses)
                {
                    var confirmResult = MessageBox.Show("该课程还有未完成的排课安排，确定要删除吗？",
                        "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                    if (confirmResult != MessageBoxResult.Yes) return;
                }

                // 软删除：设置IsActive为false
                SelectedCourse.IsActive = false;
                SelectedCourse.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();
                await LoadCoursesAsync();

                SelectedCourse = null;
                MessageBox.Show("课程删除成功", "删除成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除课程失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }
}
