using System;
using System.Collections.Generic;

namespace AutoClassTime.Models
{
    /// <summary>
    /// 解析后的排课数据
    /// </summary>
    public class ParsedScheduleData
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public List<ParsedTimeSlot> TimeSlots { get; set; } = new List<ParsedTimeSlot>();
        public List<StudentRequirement> StudentRequirements { get; set; } = new List<StudentRequirement>();
        public List<StudentConstraint> StudentConstraints { get; set; } = new List<StudentConstraint>();
    }

    /// <summary>
    /// 解析的时间槽
    /// </summary>
    public class ParsedTimeSlot
    {
        public string Subject { get; set; }
        public string Date { get; set; }
        public string StartTime { get; set; }
        public string EndTime { get; set; }
        public List<string> Students { get; set; } = new List<string>();
        public double Duration { get; set; }
        public bool IsUsed { get; set; }
        public bool CanGroup { get; set; } // 是否可以拼班
        public string Teacher { get; set; } // 教师姓名
    }

    /// <summary>
    /// 学生课程需求
    /// </summary>
    public class StudentRequirement
    {
        public string StudentName { get; set; }
        public string Subject { get; set; }
        public int ClassCount { get; set; }
        public string SpecialRequirements { get; set; }
    }

    /// <summary>
    /// 学生约束条件
    /// </summary>
    public class StudentConstraint
    {
        public string StudentName { get; set; }
        public string Description { get; set; }
        public bool IsRestriction { get; set; }
        public string Date { get; set; }
        public string TimeRange { get; set; }
        public string ConstraintType { get; set; } // 约束类型：时间限制、拼班要求、时间偏好等
        public string TargetStudent { get; set; } // 拼班目标学生
        public string TargetSubject { get; set; } // 拼班目标科目
    }

    /// <summary>
    /// 排课结果
    /// </summary>
    public class ScheduleResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public Dictionary<string, List<StudentClassInfo>> StudentSchedules { get; set; } = new Dictionary<string, List<StudentClassInfo>>();
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 学生课程信息
    /// </summary>
    public class StudentClassInfo
    {
        public string StudentName { get; set; }
        public string Subject { get; set; }
        public string Date { get; set; }
        public string StartTime { get; set; }
        public string EndTime { get; set; }
        public double Duration { get; set; }
        public bool IsGroupClass { get; set; }
        public List<string> GroupStudents { get; set; } = new List<string>();
    }
}
