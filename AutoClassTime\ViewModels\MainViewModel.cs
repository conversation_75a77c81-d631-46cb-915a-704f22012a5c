using AutoClassTime.Data;
using AutoClassTime.Services;
using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace AutoClassTime.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public class MainViewModel : ViewModelBase
    {
        private readonly ClassTimeDbContext _context;
        private readonly DatabaseService _databaseService;
        private string _statusMessage;
        private bool _isLoading;
        private ViewModelBase _currentViewModel;

        public MainViewModel()
        {
            _context = new ClassTimeDbContext();
            _databaseService = new DatabaseService();

            // 初始化命令
            ShowStudentManagementCommand = new RelayCommand(ShowStudentManagement);
            ShowCourseManagementCommand = new RelayCommand(ShowCourseManagement);
            ShowScheduleViewCommand = new RelayCommand(ShowScheduleView);
            ShowTimeSlotManagementCommand = new RelayCommand(ShowTimeSlotManagement);
            ShowTextSchedulingCommand = new RelayCommand(ShowTextScheduling);
            ExitApplicationCommand = new RelayCommand(ExitApplication);
            BackupDatabaseCommand = new RelayCommand(async () => await BackupDatabaseAsync());

            StatusMessage = "欢迎使用私教排课系统";

            // 初始化数据库
            InitializeDatabaseAsync();
        }

        #region 属性

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 当前显示的ViewModel
        /// </summary>
        public ViewModelBase CurrentViewModel
        {
            get => _currentViewModel;
            set => SetProperty(ref _currentViewModel, value);
        }

        #endregion

        #region 命令

        public ICommand ShowStudentManagementCommand { get; }
        public ICommand ShowCourseManagementCommand { get; }
        public ICommand ShowScheduleViewCommand { get; }
        public ICommand ShowTimeSlotManagementCommand { get; }
        public ICommand ShowTextSchedulingCommand { get; }
        public ICommand ExitApplicationCommand { get; }
        public ICommand BackupDatabaseCommand { get; }

        #endregion

        #region 方法

        /// <summary>
        /// 初始化数据库
        /// </summary>
        private async void InitializeDatabaseAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在初始化数据库...";

                await _databaseService.InitializeDatabaseAsync();

                StatusMessage = "数据库初始化完成";

                // 默认显示智能文本解析排课
                try
                {
                    ShowTextScheduling();
                }
                catch (Exception textEx)
                {
                    StatusMessage = $"智能排课初始化失败，回退到学生管理: {textEx.Message}";
                    ShowStudentManagement();
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"数据库初始化失败: {ex.Message}";

                // 如果是TimeSpan相关的错误，提供修复选项
                if (ex.Message.Contains("TimeSpan") || ex.Message.Contains("order by"))
                {
                    var result = MessageBox.Show(
                        "检测到数据库兼容性问题，可能是TimeSpan排序导致的。\n" +
                        "是否要自动修复？这将重新创建数据库。\n\n" +
                        "注意：这将清除现有数据，建议先备份。",
                        "数据库修复",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        DatabaseFix.FixTimeSpanSortingIssue();
                        // 重新尝试初始化
                        await _databaseService.InitializeDatabaseAsync();
                        StatusMessage = "数据库修复并初始化完成";
                        try
                        {
                            ShowTextScheduling();
                        }
                        catch (Exception textEx)
                        {
                            StatusMessage = $"智能排课初始化失败，回退到学生管理: {textEx.Message}";
                            ShowStudentManagement();
                        }
                    }
                }
                else
                {
                    MessageBox.Show($"数据库初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 显示学生管理
        /// </summary>
        private void ShowStudentManagement()
        {
            try
            {
                CurrentViewModel = new StudentManagementViewModel(_context);
                StatusMessage = "学生管理";
            }
            catch (Exception ex)
            {
                StatusMessage = $"打开学生管理失败: {ex.Message}";
                MessageBox.Show($"打开学生管理失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示课程管理
        /// </summary>
        private void ShowCourseManagement()
        {
            try
            {
                CurrentViewModel = new CourseManagementViewModel(_context);
                StatusMessage = "课程管理";
            }
            catch (Exception ex)
            {
                StatusMessage = $"打开课程管理失败: {ex.Message}";
                MessageBox.Show($"打开课程管理失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示排课视图
        /// </summary>
        private void ShowScheduleView()
        {
            try
            {
                var conflictService = new ConflictDetectionService(_context);
                var schedulingService = new SchedulingService(_context, conflictService);
                CurrentViewModel = new ScheduleViewModel(_context, schedulingService, conflictService);
                StatusMessage = "排课管理";
            }
            catch (Exception ex)
            {
                StatusMessage = $"打开排课管理失败: {ex.Message}";
                MessageBox.Show($"打开排课管理失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示时间槽管理
        /// </summary>
        private void ShowTimeSlotManagement()
        {
            try
            {
                CurrentViewModel = new TimeSlotManagementViewModel(_context);
                StatusMessage = "时间槽管理";
            }
            catch (Exception ex)
            {
                StatusMessage = $"打开时间槽管理失败: {ex.Message}";
                MessageBox.Show($"打开时间槽管理失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示文本解析排课
        /// </summary>
        private void ShowTextScheduling()
        {
            try
            {
                StatusMessage = "正在初始化智能文本解析排课...";
                CurrentViewModel = new TextSchedulingViewModel();
                StatusMessage = "智能文本解析排课";
            }
            catch (Exception ex)
            {
                StatusMessage = $"打开文本解析排课失败: {ex.Message}";
                MessageBox.Show($"打开文本解析排课失败: {ex.Message}\n\n堆栈跟踪:\n{ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 回退到排课管理
                try
                {
                    var conflictService = new ConflictDetectionService(_context);
                    var schedulingService = new SchedulingService(_context, conflictService);
                    CurrentViewModel = new ScheduleViewModel(_context, schedulingService, conflictService);
                    StatusMessage = "已回退到排课管理";
                }
                catch
                {
                    StatusMessage = "初始化失败，请重启应用程序";
                }
            }
        }

        /// <summary>
        /// 退出应用程序
        /// </summary>
        private void ExitApplication()
        {
            var result = MessageBox.Show("确定要退出应用程序吗？", "确认退出", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                Application.Current.Shutdown();
            }
        }

        /// <summary>
        /// 备份数据库
        /// </summary>
        private async Task BackupDatabaseAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在备份数据库...";

                var success = await _databaseService.BackupDatabaseAsync();

                if (success)
                {
                    StatusMessage = "数据库备份成功";
                    MessageBox.Show("数据库备份成功！", "备份完成", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    StatusMessage = "数据库备份失败";
                    MessageBox.Show("数据库备份失败！", "备份失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"备份失败: {ex.Message}";
                MessageBox.Show($"备份失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            _context?.Dispose();
            _databaseService?.Dispose();
        }

        #endregion
    }
}
