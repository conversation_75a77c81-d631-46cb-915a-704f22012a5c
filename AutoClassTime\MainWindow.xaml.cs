﻿using System.Windows;
using AutoClassTime.ViewModels;

namespace AutoClassTime
{
    /// <summary>
    /// 主窗口
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            DataContext = new MainViewModel();
        }

        protected override void OnClosed(System.EventArgs e)
        {
            base.OnClosed(e);

            // 清理资源
            if (DataContext is MainViewModel mainViewModel)
            {
                mainViewModel.Dispose();
            }
        }
    }
}
