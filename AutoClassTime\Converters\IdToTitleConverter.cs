using System;
using System.Globalization;
using System.Windows.Data;

namespace AutoClassTime.Converters
{
    /// <summary>
    /// ID到标题的转换器
    /// </summary>
    public class IdToTitleConverter : IValueConverter
    {
        public static readonly IdToTitleConverter Instance = new IdToTitleConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int id)
            {
                return id == 0 ? "添加学生" : "编辑学生";
            }
            return "编辑学生";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
