using System;
using System.Globalization;
using System.Windows.Data;
using AutoClassTime.Models;

namespace AutoClassTime.Converters
{
    /// <summary>
    /// 课程状态到文本的转换器
    /// </summary>
    public class StatusToTextConverter : IValueConverter
    {
        public static readonly StatusToTextConverter Instance = new StatusToTextConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ClassStatus status)
            {
                return status switch
                {
                    ClassStatus.Scheduled => "已安排",
                    ClassStatus.InProgress => "进行中",
                    ClassStatus.Completed => "已完成",
                    ClassStatus.Cancelled => "已取消",
                    ClassStatus.StudentAbsent => "学生缺席",
                    _ => "未知"
                };
            }
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
