using Microsoft.EntityFrameworkCore;
using AutoClassTime.Data;
using AutoClassTime.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AutoClassTime.Services
{
    /// <summary>
    /// 排课服务
    /// </summary>
    public class SchedulingService
    {
        private readonly ClassTimeDbContext _context;
        private readonly ConflictDetectionService _conflictService;

        public SchedulingService(ClassTimeDbContext context, ConflictDetectionService conflictService)
        {
            _context = context;
            _conflictService = conflictService;
        }

        /// <summary>
        /// 自动排课
        /// </summary>
        public async Task<SchedulingResult> AutoScheduleAsync(SchedulingRequest request)
        {
            try
            {
                var result = new SchedulingResult();

                // 获取学生信息
                var student = await _context.Students.FindAsync(request.StudentId);
                if (student == null)
                {
                    result.Success = false;
                    result.Message = "学生不存在";
                    return result;
                }

                // 获取课程信息
                var course = await _context.Courses.FindAsync(request.CourseId);
                if (course == null)
                {
                    result.Success = false;
                    result.Message = "课程不存在";
                    return result;
                }

                // 获取可用时间槽
                var availableTimeSlots = await _conflictService.GetAvailableTimeSlotsForStudentAsync(
                    request.StudentId, request.StartDate, request.EndDate);

                if (!availableTimeSlots.Any())
                {
                    result.Success = false;
                    result.Message = "在指定时间范围内没有可用的时间槽";
                    return result;
                }

                // 过滤符合课程时长的时间槽
                var suitableTimeSlots = availableTimeSlots
                    .Where(ts => ts.DurationMinutes >= course.DurationMinutes)
                    .ToList();

                if (!suitableTimeSlots.Any())
                {
                    result.Success = false;
                    result.Message = "没有找到符合课程时长要求的时间槽";
                    return result;
                }

                // 根据偏好时间进行排序
                var sortedTimeSlots = SortTimeSlotsByPreference(suitableTimeSlots, request.PreferredTimes);

                // 尝试安排课程
                var selectedTimeSlot = sortedTimeSlots.First();

                var classRecord = new ClassRecord
                {
                    StudentId = request.StudentId,
                    CourseId = request.CourseId,
                    TimeSlotId = selectedTimeSlot.Id,
                    SecondStudentId = request.SecondStudentId,
                    Status = ClassStatus.Scheduled,
                    Notes = request.Notes,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                // 最终冲突检测
                ConflictResult conflictResult;
                if (request.SecondStudentId.HasValue)
                {
                    conflictResult = await _conflictService.CheckPairClassConflictAsync(
                        request.StudentId, request.SecondStudentId.Value, selectedTimeSlot.Id);
                }
                else
                {
                    conflictResult = await _conflictService.CheckStudentConflictAsync(
                        request.StudentId, selectedTimeSlot.Id);
                }

                if (conflictResult.HasConflict)
                {
                    result.Success = false;
                    result.Message = $"排课失败: {conflictResult.Message}";
                    return result;
                }

                // 保存课程记录
                _context.ClassRecords.Add(classRecord);
                await _context.SaveChangesAsync();

                result.Success = true;
                result.Message = "排课成功";
                result.ClassRecord = classRecord;
                result.SelectedTimeSlot = selectedTimeSlot;

                return result;
            }
            catch (Exception ex)
            {
                return new SchedulingResult
                {
                    Success = false,
                    Message = $"排课过程中发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 批量自动排课
        /// </summary>
        public async Task<BatchSchedulingResult> BatchAutoScheduleAsync(List<SchedulingRequest> requests)
        {
            var result = new BatchSchedulingResult();
            var successfulSchedules = new List<SchedulingResult>();
            var failedSchedules = new List<SchedulingResult>();

            foreach (var request in requests)
            {
                var scheduleResult = await AutoScheduleAsync(request);

                if (scheduleResult.Success)
                {
                    successfulSchedules.Add(scheduleResult);
                }
                else
                {
                    failedSchedules.Add(scheduleResult);
                }
            }

            result.SuccessfulSchedules = successfulSchedules;
            result.FailedSchedules = failedSchedules;
            result.TotalRequests = requests.Count;
            result.SuccessCount = successfulSchedules.Count;
            result.FailureCount = failedSchedules.Count;

            return result;
        }

        /// <summary>
        /// 获取推荐的时间槽
        /// </summary>
        public async Task<List<TimeSlot>> GetRecommendedTimeSlotsAsync(int studentId, int courseId, DateTime startDate, DateTime endDate, int maxResults = 10)
        {
            try
            {
                // 获取课程信息
                var course = await _context.Courses.FindAsync(courseId);
                if (course == null) return new List<TimeSlot>();

                // 获取学生可用时间槽
                var availableTimeSlots = await _conflictService.GetAvailableTimeSlotsForStudentAsync(studentId, startDate, endDate);

                // 过滤符合课程时长的时间槽
                var suitableTimeSlots = availableTimeSlots
                    .Where(ts => ts.DurationMinutes >= course.DurationMinutes)
                    .ToList();

                // 根据历史偏好进行排序（这里可以根据学生的历史课程时间进行智能推荐）
                var recommendedTimeSlots = await SortByStudentPreferenceAsync(studentId, suitableTimeSlots);

                return recommendedTimeSlots.Take(maxResults).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取推荐时间槽时发生错误: {ex.Message}");
                return new List<TimeSlot>();
            }
        }

        /// <summary>
        /// 根据偏好时间排序时间槽
        /// </summary>
        private List<TimeSlot> SortTimeSlotsByPreference(List<TimeSlot> timeSlots, List<TimeSpan> preferredTimes)
        {
            if (preferredTimes == null || !preferredTimes.Any())
            {
                // 在内存中排序，避免SQLite的TimeSpan排序问题
                return timeSlots.OrderBy(ts => ts.Date).ThenBy(ts => ts.StartTime).ToList();
            }

            return timeSlots.OrderBy(ts =>
            {
                // 计算与偏好时间的最小差距
                var minDifference = preferredTimes.Min(pt => Math.Abs((ts.StartTime - pt).TotalMinutes));
                return minDifference;
            })
            .ThenBy(ts => ts.Date)
            .ToList();
        }

        /// <summary>
        /// 根据学生历史偏好排序
        /// </summary>
        private async Task<List<TimeSlot>> SortByStudentPreferenceAsync(int studentId, List<TimeSlot> timeSlots)
        {
            try
            {
                // 获取学生的历史课程记录
                var historicalRecords = await _context.ClassRecords
                    .Include(cr => cr.TimeSlot)
                    .Where(cr => cr.StudentId == studentId && cr.Status == ClassStatus.Completed)
                    .ToListAsync();

                if (!historicalRecords.Any())
                {
                    // 如果没有历史记录，在内存中按时间顺序排序
                    return timeSlots.OrderBy(ts => ts.Date).ThenBy(ts => ts.StartTime).ToList();
                }

                // 计算学生偏好的时间段
                var preferredTimes = historicalRecords
                    .GroupBy(hr => hr.TimeSlot.StartTime.Hours)
                    .OrderByDescending(g => g.Count())
                    .Select(g => TimeSpan.FromHours(g.Key))
                    .ToList();

                return SortTimeSlotsByPreference(timeSlots, preferredTimes);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据学生偏好排序时发生错误: {ex.Message}");
                // 在内存中排序，避免SQLite的TimeSpan排序问题
                return timeSlots.OrderBy(ts => ts.Date).ThenBy(ts => ts.StartTime).ToList();
            }
        }

        /// <summary>
        /// 取消课程
        /// </summary>
        public async Task<bool> CancelClassAsync(int classRecordId, string reason = null)
        {
            try
            {
                var classRecord = await _context.ClassRecords.FindAsync(classRecordId);
                if (classRecord == null) return false;

                classRecord.Status = ClassStatus.Cancelled;
                classRecord.Notes = string.IsNullOrEmpty(reason) ? classRecord.Notes : $"{classRecord.Notes}\n取消原因: {reason}";
                classRecord.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取消课程时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 恢复已取消的课程
        /// </summary>
        public async Task<SchedulingResult> RestoreClassAsync(int classRecordId)
        {
            try
            {
                var classRecord = await _context.ClassRecords
                    .Include(cr => cr.Student)
                    .Include(cr => cr.SecondStudent)
                    .Include(cr => cr.Course)
                    .Include(cr => cr.TimeSlot)
                    .FirstOrDefaultAsync(cr => cr.Id == classRecordId);

                if (classRecord == null)
                {
                    return new SchedulingResult { Success = false, Message = "课程记录不存在" };
                }

                if (classRecord.Status != ClassStatus.Cancelled)
                {
                    return new SchedulingResult { Success = false, Message = "只能恢复已取消的课程" };
                }

                // 检测恢复后是否有冲突
                ConflictResult conflictResult;
                if (classRecord.IsPairClass)
                {
                    conflictResult = await _conflictService.CheckPairClassConflictAsync(
                        classRecord.StudentId, classRecord.SecondStudentId.Value, classRecord.TimeSlotId, classRecordId);
                }
                else
                {
                    conflictResult = await _conflictService.CheckStudentConflictAsync(
                        classRecord.StudentId, classRecord.TimeSlotId, classRecordId);
                }

                if (conflictResult.HasConflict)
                {
                    return new SchedulingResult { Success = false, Message = $"恢复失败，存在时间冲突: {conflictResult.Message}" };
                }

                // 恢复课程状态
                classRecord.Status = ClassStatus.Scheduled;
                classRecord.Notes = $"{classRecord.Notes}\n{DateTime.Now:yyyy-MM-dd HH:mm:ss} 课程已恢复";
                classRecord.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return new SchedulingResult
                {
                    Success = true,
                    Message = "课程恢复成功",
                    ClassRecord = classRecord,
                    SelectedTimeSlot = classRecord.TimeSlot
                };
            }
            catch (Exception ex)
            {
                return new SchedulingResult
                {
                    Success = false,
                    Message = $"恢复课程时发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 重新安排课程
        /// </summary>
        public async Task<SchedulingResult> RescheduleClassAsync(int classRecordId, int newTimeSlotId)
        {
            try
            {
                var classRecord = await _context.ClassRecords
                    .Include(cr => cr.Student)
                    .Include(cr => cr.Course)
                    .FirstOrDefaultAsync(cr => cr.Id == classRecordId);

                if (classRecord == null)
                {
                    return new SchedulingResult { Success = false, Message = "课程记录不存在" };
                }

                // 检测新时间槽的冲突
                ConflictResult conflictResult;
                if (classRecord.IsPairClass)
                {
                    conflictResult = await _conflictService.CheckPairClassConflictAsync(
                        classRecord.StudentId, classRecord.SecondStudentId.Value, newTimeSlotId, classRecordId);
                }
                else
                {
                    conflictResult = await _conflictService.CheckStudentConflictAsync(
                        classRecord.StudentId, newTimeSlotId, classRecordId);
                }

                if (conflictResult.HasConflict)
                {
                    return new SchedulingResult { Success = false, Message = conflictResult.Message };
                }

                // 更新时间槽
                classRecord.TimeSlotId = newTimeSlotId;
                classRecord.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                var newTimeSlot = await _context.TimeSlots.FindAsync(newTimeSlotId);

                return new SchedulingResult
                {
                    Success = true,
                    Message = "重新安排成功",
                    ClassRecord = classRecord,
                    SelectedTimeSlot = newTimeSlot
                };
            }
            catch (Exception ex)
            {
                return new SchedulingResult
                {
                    Success = false,
                    Message = $"重新安排课程时发生错误: {ex.Message}"
                };
            }
        }
    }

    /// <summary>
    /// 排课请求
    /// </summary>
    public class SchedulingRequest
    {
        public int StudentId { get; set; }
        public int CourseId { get; set; }
        public int? SecondStudentId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<TimeSpan> PreferredTimes { get; set; } = new List<TimeSpan>();
        public string Notes { get; set; }
    }

    /// <summary>
    /// 排课结果
    /// </summary>
    public class SchedulingResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public ClassRecord ClassRecord { get; set; }
        public TimeSlot SelectedTimeSlot { get; set; }
    }

    /// <summary>
    /// 批量排课结果
    /// </summary>
    public class BatchSchedulingResult
    {
        public List<SchedulingResult> SuccessfulSchedules { get; set; } = new List<SchedulingResult>();
        public List<SchedulingResult> FailedSchedules { get; set; } = new List<SchedulingResult>();
        public int TotalRequests { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public double SuccessRate => TotalRequests > 0 ? (double)SuccessCount / TotalRequests * 100 : 0;
    }
}
