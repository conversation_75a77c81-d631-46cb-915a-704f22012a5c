using System;
using System.IO;
using System.Windows;

namespace AutoClassTime
{
    /// <summary>
    /// 数据库修复工具
    /// </summary>
    public static class DatabaseFix
    {
        /// <summary>
        /// 修复TimeSpan排序问题
        /// </summary>
        public static void FixTimeSpanSortingIssue()
        {
            try
            {
                string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                string appFolder = Path.Combine(appDataPath, "AutoClassTime");
                string dbPath = Path.Combine(appFolder, "ClassTime.db");

                if (File.Exists(dbPath))
                {
                    var result = MessageBox.Show(
                        "检测到旧版本数据库文件，需要重新创建以修复TimeSpan排序问题。\n" +
                        "这将删除现有数据，是否继续？\n\n" +
                        "建议：如果有重要数据，请先使用'备份数据'功能进行备份。",
                        "数据库修复",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // 备份旧数据库
                        string backupPath = Path.Combine(appFolder, "Backups");
                        if (!Directory.Exists(backupPath))
                        {
                            Directory.CreateDirectory(backupPath);
                        }

                        string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                        string backupFile = Path.Combine(backupPath, $"ClassTime_BeforeFix_{timestamp}.db");
                        File.Copy(dbPath, backupFile);

                        // 删除旧数据库
                        File.Delete(dbPath);

                        MessageBox.Show(
                            $"旧数据库已备份到：{backupFile}\n" +
                            "应用程序将在下次启动时创建新的数据库。",
                            "修复完成",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("未找到数据库文件，无需修复。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"修复过程中发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 检查是否需要修复
        /// </summary>
        public static bool NeedsFix()
        {
            try
            {
                string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                string appFolder = Path.Combine(appDataPath, "AutoClassTime");
                string dbPath = Path.Combine(appFolder, "ClassTime.db");

                return File.Exists(dbPath);
            }
            catch
            {
                return false;
            }
        }
    }
}
