using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AutoClassTime.Models
{
    /// <summary>
    /// 课程实体类
    /// </summary>
    public class Course
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 课程时长（分钟）
        /// </summary>
        [Required]
        public int DurationMinutes { get; set; }

        /// <summary>
        /// 课程价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 课程颜色（用于日历显示）
        /// </summary>
        [StringLength(7)]
        public string Color { get; set; } = "#3498db";

        /// <summary>
        /// 是否支持双人课程
        /// </summary>
        public bool SupportsPairClass { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        // 导航属性：课程的上课记录
        public virtual ICollection<ClassRecord> ClassRecords { get; set; } = new List<ClassRecord>();
    }
}
