using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using AutoClassTime.Models;

namespace AutoClassTime.Converters
{
    /// <summary>
    /// 课程状态到背景色的转换器
    /// </summary>
    public class StatusToBackgroundConverter : IValueConverter
    {
        public static readonly StatusToBackgroundConverter Instance = new StatusToBackgroundConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ClassStatus status)
            {
                return status switch
                {
                    ClassStatus.Scheduled => new SolidColorBrush(Colors.White),
                    ClassStatus.InProgress => new SolidColorBrush(Color.FromRgb(255, 248, 220)), // 浅黄色
                    ClassStatus.Completed => new SolidColorBrush(Color.FromRgb(240, 255, 240)), // 浅绿色
                    ClassStatus.Cancelled => new SolidColorBrush(Color.FromRgb(255, 240, 240)), // 浅红色
                    ClassStatus.StudentAbsent => new SolidColorBrush(Color.FromRgb(245, 245, 245)), // 浅灰色
                    _ => new SolidColorBrush(Colors.White)
                };
            }
            return new SolidColorBrush(Colors.White);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
