using System;
using System.ComponentModel.DataAnnotations;

namespace AutoClassTime.Models
{
    /// <summary>
    /// 时间槽实体类
    /// </summary>
    public class TimeSlot
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [Required]
        public TimeSpan StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [Required]
        public TimeSpan EndTime { get; set; }

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsAvailable { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(200)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 获取完整的开始时间
        /// </summary>
        public DateTime StartDateTime => Date.Date + StartTime;

        /// <summary>
        /// 获取完整的结束时间
        /// </summary>
        public DateTime EndDateTime => Date.Date + EndTime;

        /// <summary>
        /// 获取时间槽持续时间（分钟）
        /// </summary>
        public int DurationMinutes => (int)(EndTime - StartTime).TotalMinutes;
    }
}
