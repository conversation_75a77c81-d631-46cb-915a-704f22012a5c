# 智能排课标准文本模板

## 📋 模板格式说明

### 基本结构
```
教师资源:
[科目][教师姓名]: [时间安排]

学生需求:
学生[姓名]: 需要上 [数量] 节[科目]课，[数量] 节[科目]课。

[学生姓名]的特殊要求: [约束条件]

排课规则:
[通用规则说明]

任务: [排课目标]
```

## 🎯 标准模板

```
教师资源:

语文吴老师: 周六 10:00-12:00
数学郑老师: 周日 13:00-15:00, 15:00-17:00
英语李老师: 周六 15:00-17:00, 周日 10:00-12:00
物理冯老师: 周六 13:00-15:00 (可拼班), 周日 10:00-12:00
化学王老师: 周六 10:00-12:00 (可拼班), 周日 15:00-17:00
道法张老师: 周日 13:00-15:00

学生需求:

学生大强: 需要上 1 节物理课，1 节数学课。
学生小雨: 需要上 1 节物理课，1 节语文课。
学生小明: 需要上 2 节英语课，1 节化学课。
学生小红: 需要上 1 节道法课，1 节化学课。

大强的特殊要求: 不想在周日上课。
小雨的特殊要求: 希望和 大强 一起上物理课。
小明的特殊要求: 只能在周六上课。
小红的特殊要求: 希望和 小明 一起上化学课。

排课规则:

所有课程时长均为2小时。
同一学生不能在同一天上同一科目的课。
拼班课程最多2人一起上课。

任务: 为所有学生安排课程，优先满足特殊要求。
```

## 📝 模板字段说明

### 1. 教师资源部分
**格式**: `[科目][教师姓名]: [时间安排]`

**示例**:
- `语文吴老师: 周六 10:00-12:00`
- `数学郑老师: 周日 13:00-15:00, 15:00-17:00`
- `物理冯老师: 周六 13:00-15:00 (可拼班), 周日 10:00-12:00`

**说明**:
- 科目：语文、数学、英语、物理、化学、道法等
- 教师姓名：支持中文姓名
- 时间格式：周X HH:MM-HH:MM
- 多个时间用逗号分隔
- 可拼班标注：(可拼班)

### 2. 学生需求部分
**格式**: `学生[姓名]: 需要上 [数量] 节[科目]课，[数量] 节[科目]课。`

**示例**:
- `学生大强: 需要上 1 节物理课，1 节数学课。`
- `学生小明: 需要上 2 节英语课，1 节化学课。`

**说明**:
- 学生姓名：支持中文姓名
- 课程数量：数字
- 科目：与教师资源中的科目对应

### 3. 特殊要求部分
**格式**: `[学生姓名]的特殊要求: [约束条件]`

**常见约束类型**:
- 时间限制：`不想在周日上课`、`只能在周六上课`
- 拼班要求：`希望和 [学生姓名] 一起上[科目]课`
- 时间偏好：`希望在上午上课`、`希望在下午上课`

### 4. 排课规则部分
**通用规则**:
- 课程时长说明
- 冲突避免规则
- 拼班规则
- 其他约束条件

### 5. 任务部分
**格式**: `任务: [排课目标]`

**示例**:
- `任务: 为所有学生安排课程，优先满足特殊要求。`
- `任务: 为大强和小雨安排所有课程。`

## 🔧 支持的功能

### 时间格式
- **标准格式**: 周六 10:00-12:00
- **多时间段**: 周日 13:00-15:00, 15:00-17:00
- **跨天支持**: 支持周一到周日

### 拼班支持
- **标注方式**: (可拼班)
- **拼班要求**: 希望和 [学生] 一起上[科目]课
- **自动匹配**: 系统自动匹配拼班需求

### 约束处理
- **时间约束**: 不想在某天上课
- **偏好设置**: 希望在某时间段上课
- **拼班约束**: 指定拼班伙伴

## 📊 模板优势

### 1. 结构清晰
- 分块明确，易于理解
- 格式统一，便于解析
- 信息完整，覆盖全面

### 2. 灵活性强
- 支持多种约束条件
- 支持复杂的拼班需求
- 支持个性化要求

### 3. 易于扩展
- 可以添加新的科目
- 可以增加新的约束类型
- 可以扩展时间格式

## 🎯 使用建议

### 1. 填写顺序
1. 先填写教师资源（确定可用时间）
2. 再填写学生需求（明确课程需求）
3. 然后添加特殊要求（个性化约束）
4. 最后设置排课规则（通用约束）

### 2. 注意事项
- 教师姓名要唯一
- 学生姓名要唯一
- 时间格式要统一
- 科目名称要一致

### 3. 最佳实践
- 优先安排拼班课程
- 考虑学生的时间偏好
- 平衡教师的工作负荷
- 避免时间冲突

---

**这个标准模板将作为智能排课系统的输入格式，确保解析的准确性和结果的优化！** 🎉
