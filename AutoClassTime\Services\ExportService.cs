using Microsoft.EntityFrameworkCore;
using AutoClassTime.Data;
using AutoClassTime.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AutoClassTime.Services
{
    /// <summary>
    /// 数据导出服务
    /// </summary>
    public class ExportService
    {
        private readonly ClassTimeDbContext _context;

        public ExportService(ClassTimeDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 导出课程安排到CSV
        /// </summary>
        public async Task<string> ExportScheduleToCsvAsync(DateTime startDate, DateTime endDate, string filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                    string appFolder = Path.Combine(appDataPath, "AutoClassTime", "Exports");
                    
                    if (!Directory.Exists(appFolder))
                    {
                        Directory.CreateDirectory(appFolder);
                    }

                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    filePath = Path.Combine(appFolder, $"课程安排_{timestamp}.csv");
                }

                var classRecords = await _context.ClassRecords
                    .Include(cr => cr.Student)
                    .Include(cr => cr.SecondStudent)
                    .Include(cr => cr.Course)
                    .Include(cr => cr.TimeSlot)
                    .Where(cr => cr.TimeSlot.Date >= startDate.Date && 
                                cr.TimeSlot.Date <= endDate.Date &&
                                cr.Status != ClassStatus.Cancelled)
                    .OrderBy(cr => cr.TimeSlot.Date)
                    .ThenBy(cr => cr.TimeSlot.StartTime)
                    .ToListAsync();

                var csv = new StringBuilder();
                
                // 添加CSV头部
                csv.AppendLine("日期,开始时间,结束时间,学生姓名,第二学生,课程名称,课程状态,备注");

                foreach (var record in classRecords)
                {
                    var line = $"{record.TimeSlot.Date:yyyy-MM-dd}," +
                              $"{record.TimeSlot.StartTime:hh\\:mm}," +
                              $"{record.TimeSlot.EndTime:hh\\:mm}," +
                              $"\"{record.Student.Name}\"," +
                              $"\"{record.SecondStudent?.Name ?? ""}\"," +
                              $"\"{record.Course.Name}\"," +
                              $"{GetStatusText(record.Status)}," +
                              $"\"{record.Notes ?? ""}\"";
                    
                    csv.AppendLine(line);
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"导出CSV文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出学生列表到CSV
        /// </summary>
        public async Task<string> ExportStudentsToCsvAsync(string filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                    string appFolder = Path.Combine(appDataPath, "AutoClassTime", "Exports");
                    
                    if (!Directory.Exists(appFolder))
                    {
                        Directory.CreateDirectory(appFolder);
                    }

                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    filePath = Path.Combine(appFolder, $"学生列表_{timestamp}.csv");
                }

                var students = await _context.Students
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.Name)
                    .ToListAsync();

                var csv = new StringBuilder();
                
                // 添加CSV头部
                csv.AppendLine("姓名,电话,邮箱,地址,备注,创建时间");

                foreach (var student in students)
                {
                    var line = $"\"{student.Name}\"," +
                              $"\"{student.Phone ?? ""}\"," +
                              $"\"{student.Email ?? ""}\"," +
                              $"\"{student.Address ?? ""}\"," +
                              $"\"{student.Notes ?? ""}\"," +
                              $"{student.CreatedAt:yyyy-MM-dd HH:mm:ss}";
                    
                    csv.AppendLine(line);
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"导出学生列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出课程统计报告
        /// </summary>
        public async Task<string> ExportCourseStatisticsAsync(DateTime startDate, DateTime endDate, string filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                    string appFolder = Path.Combine(appDataPath, "AutoClassTime", "Exports");
                    
                    if (!Directory.Exists(appFolder))
                    {
                        Directory.CreateDirectory(appFolder);
                    }

                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    filePath = Path.Combine(appFolder, $"课程统计_{timestamp}.csv");
                }

                var statistics = await _context.ClassRecords
                    .Include(cr => cr.Course)
                    .Include(cr => cr.TimeSlot)
                    .Where(cr => cr.TimeSlot.Date >= startDate.Date && 
                                cr.TimeSlot.Date <= endDate.Date)
                    .GroupBy(cr => new { cr.CourseId, cr.Course.Name, cr.Status })
                    .Select(g => new
                    {
                        CourseId = g.Key.CourseId,
                        CourseName = g.Key.Name,
                        Status = g.Key.Status,
                        Count = g.Count(),
                        TotalHours = g.Sum(cr => cr.Course.DurationMinutes) / 60.0
                    })
                    .OrderBy(s => s.CourseName)
                    .ThenBy(s => s.Status)
                    .ToListAsync();

                var csv = new StringBuilder();
                
                // 添加CSV头部
                csv.AppendLine("课程名称,状态,课程数量,总时长(小时)");

                foreach (var stat in statistics)
                {
                    var line = $"\"{stat.CourseName}\"," +
                              $"{GetStatusText(stat.Status)}," +
                              $"{stat.Count}," +
                              $"{stat.TotalHours:F1}";
                    
                    csv.AppendLine(line);
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"导出课程统计失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出学生课程记录
        /// </summary>
        public async Task<string> ExportStudentRecordsAsync(int studentId, DateTime startDate, DateTime endDate, string filePath = null)
        {
            try
            {
                var student = await _context.Students.FindAsync(studentId);
                if (student == null)
                {
                    throw new Exception("学生不存在");
                }

                if (string.IsNullOrEmpty(filePath))
                {
                    string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                    string appFolder = Path.Combine(appDataPath, "AutoClassTime", "Exports");
                    
                    if (!Directory.Exists(appFolder))
                    {
                        Directory.CreateDirectory(appFolder);
                    }

                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    filePath = Path.Combine(appFolder, $"{student.Name}_课程记录_{timestamp}.csv");
                }

                var classRecords = await _context.ClassRecords
                    .Include(cr => cr.Course)
                    .Include(cr => cr.TimeSlot)
                    .Include(cr => cr.SecondStudent)
                    .Where(cr => (cr.StudentId == studentId || cr.SecondStudentId == studentId) &&
                                cr.TimeSlot.Date >= startDate.Date && 
                                cr.TimeSlot.Date <= endDate.Date)
                    .OrderBy(cr => cr.TimeSlot.Date)
                    .ThenBy(cr => cr.TimeSlot.StartTime)
                    .ToListAsync();

                var csv = new StringBuilder();
                
                // 添加CSV头部
                csv.AppendLine("日期,开始时间,结束时间,课程名称,课程类型,状态,第二学生,备注");

                foreach (var record in classRecords)
                {
                    var courseType = record.IsPairClass ? "双人课程" : "单人课程";
                    var line = $"{record.TimeSlot.Date:yyyy-MM-dd}," +
                              $"{record.TimeSlot.StartTime:hh\\:mm}," +
                              $"{record.TimeSlot.EndTime:hh\\:mm}," +
                              $"\"{record.Course.Name}\"," +
                              $"{courseType}," +
                              $"{GetStatusText(record.Status)}," +
                              $"\"{record.SecondStudent?.Name ?? ""}\"," +
                              $"\"{record.Notes ?? ""}\"";
                    
                    csv.AppendLine(line);
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"导出学生课程记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        private string GetStatusText(ClassStatus status)
        {
            return status switch
            {
                ClassStatus.Scheduled => "已安排",
                ClassStatus.InProgress => "进行中",
                ClassStatus.Completed => "已完成",
                ClassStatus.Cancelled => "已取消",
                ClassStatus.StudentAbsent => "学生缺席",
                _ => "未知"
            };
        }

        /// <summary>
        /// 获取导出文件夹路径
        /// </summary>
        public string GetExportFolderPath()
        {
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            return Path.Combine(appDataPath, "AutoClassTime", "Exports");
        }
    }
}
