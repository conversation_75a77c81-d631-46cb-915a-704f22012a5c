using Microsoft.EntityFrameworkCore;
using AutoClassTime.Models;
using System;
using System.IO;

namespace AutoClassTime.Data
{
    /// <summary>
    /// 数据库上下文类
    /// </summary>
    public class ClassTimeDbContext : DbContext
    {
        public DbSet<Student> Students { get; set; }
        public DbSet<Course> Courses { get; set; }
        public DbSet<TimeSlot> TimeSlots { get; set; }
        public DbSet<ClassRecord> ClassRecords { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // 获取应用程序数据目录
                string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                string appFolder = Path.Combine(appDataPath, "AutoClassTime");

                // 确保目录存在
                if (!Directory.Exists(appFolder))
                {
                    Directory.CreateDirectory(appFolder);
                }

                string dbPath = Path.Combine(appFolder, "ClassTime.db");
                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置Student实体
            modelBuilder.Entity<Student>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Phone).HasMaxLength(20);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Address).HasMaxLength(200);
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.HasIndex(e => e.Name);
            });

            // 配置Course实体
            modelBuilder.Entity<Course>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Color).HasMaxLength(7).HasDefaultValue("#3498db");
                entity.Property(e => e.Price).HasColumnType("decimal(10,2)");
                entity.HasIndex(e => e.Name);
            });

            // 配置TimeSlot实体
            modelBuilder.Entity<TimeSlot>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Notes).HasMaxLength(200);
                // 将TimeSpan转换为字符串存储以支持排序
                entity.Property(e => e.StartTime)
                      .HasConversion(
                          v => v.ToString(@"hh\:mm\:ss"),
                          v => TimeSpan.Parse(v));
                entity.Property(e => e.EndTime)
                      .HasConversion(
                          v => v.ToString(@"hh\:mm\:ss"),
                          v => TimeSpan.Parse(v));
                entity.HasIndex(e => new { e.Date, e.StartTime });
            });

            // 配置ClassRecord实体
            modelBuilder.Entity<ClassRecord>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Notes).HasMaxLength(500);

                // 配置外键关系
                entity.HasOne(e => e.Student)
                      .WithMany(s => s.ClassRecords)
                      .HasForeignKey(e => e.StudentId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.SecondStudent)
                      .WithMany()
                      .HasForeignKey(e => e.SecondStudentId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Course)
                      .WithMany(c => c.ClassRecords)
                      .HasForeignKey(e => e.CourseId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.TimeSlot)
                      .WithMany()
                      .HasForeignKey(e => e.TimeSlotId)
                      .OnDelete(DeleteBehavior.Restrict);

                // 创建复合索引
                entity.HasIndex(e => new { e.StudentId, e.TimeSlotId });
                entity.HasIndex(e => new { e.CourseId, e.TimeSlotId });
            });

            // 添加种子数据
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // 添加默认课程
            modelBuilder.Entity<Course>().HasData(
                new Course
                {
                    Id = 1,
                    Name = "数学辅导",
                    Description = "小学数学一对一辅导",
                    DurationMinutes = 60,
                    Price = 100,
                    Color = "#e74c3c",
                    SupportsPairClass = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Course
                {
                    Id = 2,
                    Name = "英语辅导",
                    Description = "小学英语一对一辅导",
                    DurationMinutes = 60,
                    Price = 120,
                    Color = "#3498db",
                    SupportsPairClass = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Course
                {
                    Id = 3,
                    Name = "语文辅导",
                    Description = "小学语文一对一辅导",
                    DurationMinutes = 60,
                    Price = 100,
                    Color = "#2ecc71",
                    SupportsPairClass = false,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                }
            );
        }
    }
}
