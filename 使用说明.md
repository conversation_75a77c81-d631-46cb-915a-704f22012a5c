# 私教排课系统使用说明

## 🚀 快速开始

### 1. 首次启动
- 双击运行应用程序
- 系统会自动创建数据库和默认课程
- 如果出现数据库错误，选择"是"进行自动修复

### 2. 基本操作流程

#### 步骤一：添加学生
1. 点击顶部菜单的"学生管理"
2. 点击"添加学生"按钮
3. 填写学生信息（姓名为必填项）
4. 点击"保存"

#### 步骤二：设置时间槽
1. 点击顶部菜单的"时间管理"
2. 选择日期
3. 点击"生成本周时间槽"快速创建标准时间表
4. 或点击"添加时间槽"手动添加特定时间

#### 步骤三：排课
1. 点击顶部菜单的"排课管理"
2. 选择学生和课程
3. 点击"自动排课"让系统智能安排
4. 或手动选择时间槽进行排课

## 📋 功能详解

### 学生管理
- **添加学生**：录入学生基本信息
- **编辑学生**：修改学生信息
- **删除学生**：软删除，不会真正删除数据
- **搜索学生**：按姓名、电话、邮箱搜索

### 课程管理
- **默认课程**：数学辅导、英语辅导、语文辅导
- **添加课程**：自定义课程名称、时长、价格
- **课程颜色**：用于日历显示的颜色标识
- **双人课程**：支持一对二教学模式

### 时间管理
- **时间槽**：定义可用的上课时间段
- **批量生成**：一键生成一周的标准时间表
- **灵活调整**：根据实际情况调整时间安排

### 排课管理
- **智能排课**：系统自动检测冲突并推荐最佳时间
- **手动排课**：完全自主控制排课安排
- **冲突检测**：防止时间冲突和重复预约
- **课程状态**：已安排、进行中、已完成、已取消

### 数据管理
- **自动备份**：点击"备份数据"保存到本地
- **数据导出**：导出学生列表、课程安排等CSV文件
- **本地存储**：所有数据存储在本地，无需网络

## ⚠️ 注意事项

### 数据安全
- 定期使用"备份数据"功能
- 重要数据建议多重备份
- 系统升级前务必备份

### 操作建议
- 每次操作后注意查看状态栏提示
- 删除操作会有确认提示，请仔细阅读
- 遇到错误时，查看错误信息并按提示操作

### 性能优化
- 定期清理不需要的历史数据
- 避免创建过多重复的时间槽
- 合理安排课程时长和间隔

## 🔧 故障排除

### 常见问题

**Q: 启动时提示TimeSpan排序错误？**
A: 选择"是"进行自动修复，系统会重新创建数据库。

**Q: 无法添加学生或课程？**
A: 检查必填字段是否完整，确保姓名不为空。

**Q: 排课时提示冲突？**
A: 检查学生在该时间段是否已有其他课程安排。

**Q: 数据丢失了怎么办？**
A: 查看备份文件夹，使用最近的备份文件恢复。

### 数据文件位置
- **数据库**：`%APPDATA%\AutoClassTime\ClassTime.db`
- **备份**：`%APPDATA%\AutoClassTime\Backups\`
- **导出**：`%APPDATA%\AutoClassTime\Exports\`

### 联系支持
如遇到无法解决的问题：
1. 记录错误信息截图
2. 备份当前数据
3. 联系技术支持

## 📈 使用技巧

### 高效排课
1. 先批量创建时间槽
2. 按学生偏好时间进行分组
3. 使用自动排课功能提高效率
4. 定期检查和调整排课安排

### 数据管理
1. 每周备份一次数据
2. 月底导出统计报告
3. 及时清理已取消的课程记录
4. 保持学生信息的及时更新

### 界面操作
1. 善用搜索功能快速定位
2. 利用颜色标识区分不同课程
3. 关注状态栏的操作反馈
4. 熟练使用快捷操作按钮

---

**祝您使用愉快！如有问题，请参考故障排除部分或联系技术支持。**
