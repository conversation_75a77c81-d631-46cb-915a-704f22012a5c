using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AutoClassTime.Models
{
    /// <summary>
    /// 学生实体类
    /// </summary>
    public class Student
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; }

        [StringLength(20)]
        public string Phone { get; set; }

        [StringLength(100)]
        public string Email { get; set; }

        [StringLength(200)]
        public string Address { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        // 导航属性：学生的课程记录
        public virtual ICollection<ClassRecord> ClassRecords { get; set; } = new List<ClassRecord>();
    }
}
