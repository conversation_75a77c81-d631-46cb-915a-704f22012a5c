{"version": 3, "targets": {"net5.0-windows7.0": {"Microsoft.Data.Sqlite.Core/5.0.17": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "compile": {"lib/netstandard2.0/Microsoft.Data.Sqlite.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Data.Sqlite.dll": {}}}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.EntityFrameworkCore/5.0.17": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "5.0.17", "Microsoft.EntityFrameworkCore.Analyzers": "5.0.17", "Microsoft.Extensions.Caching.Memory": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.17": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.17": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Relational/5.0.17": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "5.0.17", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.EntityFrameworkCore.Sqlite/5.0.17": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "5.0.17", "SQLitePCLRaw.bundle_e_sqlite3": "2.0.4"}, "compile": {"lib/netstandard2.1/_._": {}}, "runtime": {"lib/netstandard2.1/_._": {}}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/5.0.17": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "5.0.17", "Microsoft.DotNet.PlatformAbstractions": "3.1.6", "Microsoft.EntityFrameworkCore.Relational": "5.0.17", "Microsoft.Extensions.DependencyModel": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Sqlite.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Sqlite.dll": {}}}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {}}, "runtime": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyModel/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.Options.dll": {}}, "runtime": {"lib/net5.0/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.4": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.0.4", "SQLitePCLRaw.lib.e_sqlite3": "2.0.4", "SQLitePCLRaw.provider.dynamic_cdecl": "2.0.4"}, "compile": {"lib/netcoreapp3.1/SQLitePCLRaw.batteries_v2.dll": {}, "lib/netcoreapp3.1/SQLitePCLRaw.nativelibrary.dll": {}}, "runtime": {"lib/netcoreapp3.1/SQLitePCLRaw.batteries_v2.dll": {}, "lib/netcoreapp3.1/SQLitePCLRaw.nativelibrary.dll": {}}}, "SQLitePCLRaw.core/2.0.4": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "runtimeTargets": {"runtimes/alpine-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "alpine-x64"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-armel"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-mips64"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x86"}}}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.4": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.provider.dynamic_cdecl.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.provider.dynamic_cdecl.dll": {}}}, "System.Collections.Immutable/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {}}}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "compile": {"lib/net5.0/System.Diagnostics.DiagnosticSource.dll": {}}, "runtime": {"lib/net5.0/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}}}, "libraries": {"Microsoft.Data.Sqlite.Core/5.0.17": {"sha512": "oAMQWz6FNyKI/3c1BOxN1pSqMZnWvkxlheRHh+qsqnmpO5i3R9L1zky4zJ4CyZZJzaOgEWVrK4PMXlnxVl0xmw==", "type": "package", "path": "microsoft.data.sqlite.core/5.0.17", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.Data.Sqlite.dll", "lib/netstandard2.0/Microsoft.Data.Sqlite.xml", "microsoft.data.sqlite.core.5.0.17.nupkg.sha512", "microsoft.data.sqlite.core.nuspec"]}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"sha512": "jek4XYaQ/PGUwDKKhwR8K47Uh1189PFzMeLqO83mXrXQVIpARZCcfuDedH50YDTepBkfijCZN5U/vZi++erxtg==", "type": "package", "path": "microsoft.dotnet.platformabstractions/3.1.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/Microsoft.DotNet.PlatformAbstractions.dll", "lib/net45/Microsoft.DotNet.PlatformAbstractions.xml", "lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll", "lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.xml", "lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.dll", "lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.xml", "microsoft.dotnet.platformabstractions.3.1.6.nupkg.sha512", "microsoft.dotnet.platformabstractions.nuspec"]}, "Microsoft.EntityFrameworkCore/5.0.17": {"sha512": "cfsvkajdtqQT+aqInu5nRcUPnzdkrS4AtnQONThx0inNTBTxPXZj2/YAc6u3YuYiEidGCL3vxZOwtz3pLM57Ow==", "type": "package", "path": "microsoft.entityframeworkcore/5.0.17", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.5.0.17.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.17": {"sha512": "4GpkNWMryNmdrhK9VfgCMTqFjfMRhWUTLalCFrDcInWa90OMESyOU3rQjnYI4ghwTduWjJ2JOSrZbx/G2wv7Wg==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/5.0.17", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.5.0.17.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.17": {"sha512": "Gk+AQKXi5KDJgvakivZL1lTrVwidLEBvPg6BLoR0XZcEXytd/SGQKV0YhPAuL0m1+EVp/2DfBbJU/2Ub4Q7NJg==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/5.0.17", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.5.0.17.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/5.0.17": {"sha512": "2N0sbvF7LpTCGG/MHV/Hhvn9d5fPJhOYRNUFhJDA4MMDWvi+bCMMHi0voupR2fYb8EzfMm40kPrmIPz9zbDiaw==", "type": "package", "path": "microsoft.entityframeworkcore.relational/5.0.17", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.5.0.17.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite/5.0.17": {"sha512": "Q9MUwWrVlPOLVh11cENWQPSCH5h1IUcSBFNMenb/E1EMH47Mf0z0nz5XZOG93uEqqeUjgoAB9TwRaj1VL/vK2g==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite/5.0.17", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/_._", "microsoft.entityframeworkcore.sqlite.5.0.17.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite.Core/5.0.17": {"sha512": "ceXvWhjwHRVR6c6bQzr4Mej9vED2mC/RLitcd1IYjS0pi+0hZ/xXULbxQyQZ65bkMiQnV4IbZ5iSJJL0j2+dpQ==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite.core/5.0.17", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Sqlite.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Sqlite.xml", "microsoft.entityframeworkcore.sqlite.core.5.0.17.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.core.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"sha512": "bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "type": "package", "path": "microsoft.extensions.caching.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net461/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"sha512": "/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "type": "package", "path": "microsoft.extensions.caching.memory/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Memory.dll", "lib/net461/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"sha512": "ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"sha512": "xzFW00AZEvOXM1OX+0+AYH5op/Hf3u//e6wszBd/rK72sypD+jx5CtsHxM4BVuFBEs8SajfO4QzSJtrQaHDr4A==", "type": "package", "path": "microsoft.extensions.dependencyinjection/5.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/net5.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net5.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.5.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"sha512": "ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyModel/5.0.0": {"sha512": "umBECCoMC+sOUgm083yFr8SxTobUOcPFH4AXigdO2xJiszCHAnmeDl4qPphJt+oaJ/XIfV1wOjIts2nRnki61Q==", "type": "package", "path": "microsoft.extensions.dependencymodel/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net451/Microsoft.Extensions.DependencyModel.dll", "lib/net451/Microsoft.Extensions.DependencyModel.xml", "lib/net461/Microsoft.Extensions.DependencyModel.dll", "lib/net461/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.5.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging/5.0.0": {"sha512": "MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "type": "package", "path": "microsoft.extensions.logging/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.dll", "lib/net461/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.5.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"sha512": "NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "type": "package", "path": "microsoft.extensions.logging.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net461/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Options/5.0.0": {"sha512": "CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "type": "package", "path": "microsoft.extensions.options/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.dll", "lib/net461/Microsoft.Extensions.Options.xml", "lib/net5.0/Microsoft.Extensions.Options.dll", "lib/net5.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.5.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Primitives/5.0.0": {"sha512": "cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "type": "package", "path": "microsoft.extensions.primitives/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Primitives.dll", "lib/net461/Microsoft.Extensions.Primitives.xml", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.5.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.4": {"sha512": "f5U8Sw0lRym8tTraJ2zm6OqcDrcrEVvcKDtYlKSLs3Ox9SerkwkPXiFXb/uiW0g2tJdUw6oBhsxI/l5DoRxXMg==", "type": "package", "path": "sqlitepclraw.bundle_e_sqlite3/2.0.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/Xamarin.iOS10/SQLitePCLRaw.batteries_v2.dll", "lib/Xamarin.tvOS10/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.nativelibrary.dll", "lib/netcoreapp3.1/SQLitePCLRaw.batteries_v2.dll", "lib/netcoreapp3.1/SQLitePCLRaw.nativelibrary.dll", "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll", "sqlitepclraw.bundle_e_sqlite3.2.0.4.nupkg.sha512", "sqlitepclraw.bundle_e_sqlite3.nuspec"]}, "SQLitePCLRaw.core/2.0.4": {"sha512": "4XlDZpDAsboMD6qZQcz9AaKblKDUTVHF+8f3lvbP7QjoqSRr2Xc0Lm34IK2pjRIYnyFLhI3yOJ5YWfOiCid2yg==", "type": "package", "path": "sqlitepclraw.core/2.0.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.core.dll", "sqlitepclraw.core.2.0.4.nupkg.sha512", "sqlitepclraw.core.nuspec"]}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"sha512": "oetvmtDZOE4Nnrtxd8Trapl9geBiu0rDCUXff46qGYjnUwzaU1mZ3OHnfR402tl32rx8gBWg3n5OBRaPJRbsGw==", "type": "package", "path": "sqlitepclraw.lib.e_sqlite3/2.0.4", "files": [".nupkg.metadata", ".signature.p7s", "build/net461/SQLitePCLRaw.lib.e_sqlite3.targets", "lib/net461/_._", "lib/netstandard2.0/_._", "runtimes/alpine-x64/native/libe_sqlite3.so", "runtimes/linux-arm/native/libe_sqlite3.so", "runtimes/linux-arm64/native/libe_sqlite3.so", "runtimes/linux-armel/native/libe_sqlite3.so", "runtimes/linux-mips64/native/libe_sqlite3.so", "runtimes/linux-musl-x64/native/libe_sqlite3.so", "runtimes/linux-x64/native/libe_sqlite3.so", "runtimes/linux-x86/native/libe_sqlite3.so", "runtimes/osx-x64/native/libe_sqlite3.dylib", "runtimes/win-arm/native/e_sqlite3.dll", "runtimes/win-arm64/native/e_sqlite3.dll", "runtimes/win-x64/native/e_sqlite3.dll", "runtimes/win-x86/native/e_sqlite3.dll", "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll", "sqlitepclraw.lib.e_sqlite3.2.0.4.nupkg.sha512", "sqlitepclraw.lib.e_sqlite3.nuspec"]}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.4": {"sha512": "AY6+vv/4ji1mCkLrS6HP/88rHT9YFKRyg3LUj8RyIk6imJMUFdQDiP8rK8gq0a/0FbqspLjK1t7rtKcr7FXRYA==", "type": "package", "path": "sqlitepclraw.provider.dynamic_cdecl/2.0.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.provider.dynamic_cdecl.dll", "sqlitepclraw.provider.dynamic_cdecl.2.0.4.nupkg.sha512", "sqlitepclraw.provider.dynamic_cdecl.nuspec"]}, "System.Collections.Immutable/5.0.0": {"sha512": "FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "type": "package", "path": "system.collections.immutable/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.5.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/5.0.1": {"sha512": "uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net5.0-windows7.0": ["Microsoft.EntityFrameworkCore.Sqlite >= 5.0.17"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\autoClassTime\\AutoClassTime\\AutoClassTime.csproj", "projectName": "AutoClassTime", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\autoClassTime\\AutoClassTime\\AutoClassTime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\autoClassTime\\AutoClassTime\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net5.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0-windows7.0": {"targetAlias": "net5.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net5.0-windows7.0": {"targetAlias": "net5.0-windows", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[5.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.416\\RuntimeIdentifierGraph.json"}}}}