{"runtimeTarget": {"name": ".NETCoreApp,Version=v5.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v5.0": {}, ".NETCoreApp,Version=v5.0/win-x64": {"AutoClassTime/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": "5.0.17", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "5.0.17", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "5.0.17"}, "runtime": {"AutoClassTime.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/5.0.17": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "10.0.6.0", "fileVersion": "11.0.1722.21314"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "5.0.1722.21314"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21314"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.12.25830.2"}, "api-ms-win-core-console-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-console-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-datetime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-debug-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-errorhandling-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-fibers-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l2-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-handle-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-interlocked-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-libraryloader-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-localization-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-memory-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-namedpipe-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processenvironment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-1.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-profile-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-rtlsupport-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-sysinfo-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-timezone-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-util-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-conio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-convert-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-environment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-filesystem-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-locale-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-math-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-multibyte-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-private-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-process-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-runtime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-stdio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-time-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-utility-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "clrcompression.dll": {"fileVersion": "42.42.42.42424"}, "clretwrc.dll": {"fileVersion": "5.0.1722.21314"}, "clrjit.dll": {"fileVersion": "5.0.1722.21314"}, "coreclr.dll": {"fileVersion": "5.0.1722.21314"}, "createdump.exe": {"fileVersion": "5.0.1722.21314"}, "dbgshim.dll": {"fileVersion": "5.0.1722.21314"}, "hostfxr.dll": {"fileVersion": "5.0.1722.21314"}, "hostpolicy.dll": {"fileVersion": "5.0.1722.21314"}, "mscordaccore.dll": {"fileVersion": "5.0.1722.21314"}, "mscordaccore_amd64_amd64_5.0.1722.21314.dll": {"fileVersion": "5.0.1722.21314"}, "mscordbi.dll": {"fileVersion": "5.0.1722.21314"}, "mscorrc.dll": {"fileVersion": "5.0.1722.21314"}, "ucrtbase.dll": {"fileVersion": "10.0.22000.194"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/5.0.17": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21504"}, "DirectWriteForwarder.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21504"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21504"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "PresentationCore.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationFramework.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "PresentationUI.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "ReachFramework.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Design.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21504"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.321.7212"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.321.7212"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Drawing.Common.dll": {"assemblyVersion": "5.0.0.2", "fileVersion": "5.0.1221.52207"}, "System.Drawing.Design.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21504"}, "System.Drawing.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21504"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Printing.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.120.57516"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21504"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21504"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21504"}, "System.Windows.Forms.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21504"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "System.Windows.Presentation.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "System.Xaml.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "UIAutomationClient.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "UIAutomationProvider.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "UIAutomationTypes.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "WindowsBase.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21802"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22000.194"}, "PenImc_cor3.dll": {"fileVersion": "5.0.1722.21802"}, "PresentationNative_cor3.dll": {"fileVersion": "5.0.1222.20505"}, "vcruntime140_cor3.dll": {"fileVersion": "14.29.30139.0"}, "wpfgfx_cor3.dll": {"fileVersion": "5.0.1722.21802"}}}, "Microsoft.Data.Sqlite.Core/5.0.17": {"dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"runtime": {"lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "3.1.6.0", "fileVersion": "3.100.620.31604"}}}, "Microsoft.EntityFrameworkCore/5.0.17": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "5.0.17", "Microsoft.EntityFrameworkCore.Analyzers": "5.0.17", "Microsoft.Extensions.Caching.Memory": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.17": {"runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.17": {}, "Microsoft.EntityFrameworkCore.Relational/5.0.17": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.17", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}}, "Microsoft.EntityFrameworkCore.Sqlite/5.0.17": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "5.0.17", "SQLitePCLRaw.bundle_e_sqlite3": "2.0.4"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/5.0.17": {"dependencies": {"Microsoft.Data.Sqlite.Core": "5.0.17", "Microsoft.DotNet.PlatformAbstractions": "3.1.6", "Microsoft.EntityFrameworkCore.Relational": "5.0.17", "Microsoft.Extensions.DependencyModel": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}}}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.821.31504"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}}}, "Microsoft.Extensions.DependencyModel/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}}}, "Microsoft.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}}}, "Microsoft.Extensions.Options/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}}}, "Microsoft.Extensions.Primitives/5.0.0": {"runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "*********904"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4", "SQLitePCLRaw.lib.e_sqlite3": "2.0.4", "SQLitePCLRaw.provider.dynamic_cdecl": "2.0.4"}, "runtime": {"lib/netcoreapp3.1/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}, "lib/netcoreapp3.1/SQLitePCLRaw.nativelibrary.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "SQLitePCLRaw.core/2.0.4": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"native": {"runtimes/win-x64/native/e_sqlite3.dll": {"fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.provider.dynamic_cdecl.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "System.Collections.Immutable/5.0.0": {}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Diagnostics.DiagnosticSource/5.0.1": {}, "System.Memory/4.5.3": {}}}, "libraries": {"AutoClassTime/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/5.0.17": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/5.0.17": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.Data.Sqlite.Core/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-oAMQWz6FNyKI/3c1BOxN1pSqMZnWvkxlheRHh+qsqnmpO5i3R9L1zky4zJ4CyZZJzaOgEWVrK4PMXlnxVl0xmw==", "path": "microsoft.data.sqlite.core/5.0.17", "hashPath": "microsoft.data.sqlite.core.5.0.17.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-jek4XYaQ/PGUwDKKhwR8K47Uh1189PFzMeLqO83mXrXQVIpARZCcfuDedH50YDTepBkfijCZN5U/vZi++erxtg==", "path": "microsoft.dotnet.platformabstractions/3.1.6", "hashPath": "microsoft.dotnet.platformabstractions.3.1.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-cfsvkajdtqQT+aqInu5nRcUPnzdkrS4AtnQONThx0inNTBTxPXZj2/YAc6u3YuYiEidGCL3vxZOwtz3pLM57Ow==", "path": "microsoft.entityframeworkcore/5.0.17", "hashPath": "microsoft.entityframeworkcore.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-4GpkNWMryNmdrhK9VfgCMTqFjfMRhWUTLalCFrDcInWa90OMESyOU3rQjnYI4ghwTduWjJ2JOSrZbx/G2wv7Wg==", "path": "microsoft.entityframeworkcore.abstractions/5.0.17", "hashPath": "microsoft.entityframeworkcore.abstractions.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-Gk+AQKXi5KDJgvakivZL1lTrVwidLEBvPg6BLoR0XZcEXytd/SGQKV0YhPAuL0m1+EVp/2DfBbJU/2Ub4Q7NJg==", "path": "microsoft.entityframeworkcore.analyzers/5.0.17", "hashPath": "microsoft.entityframeworkcore.analyzers.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-2N0sbvF7LpTCGG/MHV/Hhvn9d5fPJhOYRNUFhJDA4MMDWvi+bCMMHi0voupR2fYb8EzfMm40kPrmIPz9zbDiaw==", "path": "microsoft.entityframeworkcore.relational/5.0.17", "hashPath": "microsoft.entityframeworkcore.relational.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-Q9MUwWrVlPOLVh11cENWQPSCH5h1IUcSBFNMenb/E1EMH47Mf0z0nz5XZOG93uEqqeUjgoAB9TwRaj1VL/vK2g==", "path": "microsoft.entityframeworkcore.sqlite/5.0.17", "hashPath": "microsoft.entityframeworkcore.sqlite.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-ceXvWhjwHRVR6c6bQzr4Mej9vED2mC/RLitcd1IYjS0pi+0hZ/xXULbxQyQZ65bkMiQnV4IbZ5iSJJL0j2+dpQ==", "path": "microsoft.entityframeworkcore.sqlite.core/5.0.17", "hashPath": "microsoft.entityframeworkcore.sqlite.core.5.0.17.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "path": "microsoft.extensions.caching.abstractions/5.0.0", "hashPath": "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "path": "microsoft.extensions.caching.memory/5.0.0", "hashPath": "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-xzFW00AZEvOXM1OX+0+AYH5op/Hf3u//e6wszBd/rK72sypD+jx5CtsHxM4BVuFBEs8SajfO4QzSJtrQaHDr4A==", "path": "microsoft.extensions.dependencyinjection/5.0.2", "hashPath": "microsoft.extensions.dependencyinjection.5.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-umBECCoMC+sOUgm083yFr8SxTobUOcPFH4AXigdO2xJiszCHAnmeDl4qPphJt+oaJ/XIfV1wOjIts2nRnki61Q==", "path": "microsoft.extensions.dependencymodel/5.0.0", "hashPath": "microsoft.extensions.dependencymodel.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "path": "microsoft.extensions.logging/5.0.0", "hashPath": "microsoft.extensions.logging.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "path": "microsoft.extensions.logging.abstractions/5.0.0", "hashPath": "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "path": "microsoft.extensions.options/5.0.0", "hashPath": "microsoft.extensions.options.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-f5U8Sw0lRym8tTraJ2zm6OqcDrcrEVvcKDtYlKSLs3Ox9SerkwkPXiFXb/uiW0g2tJdUw6oBhsxI/l5DoRxXMg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.core/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-4XlDZpDAsboMD6qZQcz9AaKblKDUTVHF+8f3lvbP7QjoqSRr2Xc0Lm34IK2pjRIYnyFLhI3yOJ5YWfOiCid2yg==", "path": "sqlitepclraw.core/2.0.4", "hashPath": "sqlitepclraw.core.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-oetvmtDZOE4Nnrtxd8Trapl9geBiu0rDCUXff46qGYjnUwzaU1mZ3OHnfR402tl32rx8gBWg3n5OBRaPJRbsGw==", "path": "sqlitepclraw.lib.e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-AY6+vv/4ji1mCkLrS6HP/88rHT9YFKRyg3LUj8RyIk6imJMUFdQDiP8rK8gq0a/0FbqspLjK1t7rtKcr7FXRYA==", "path": "sqlitepclraw.provider.dynamic_cdecl/2.0.4", "hashPath": "sqlitepclraw.provider.dynamic_cdecl.2.0.4.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "path": "system.diagnostics.diagnosticsource/5.0.1", "hashPath": "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"], "win-x64-aot": ["win-aot", "win-x64", "win", "aot", "any", "base"], "win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win7-x64": ["win7", "win-x64", "win", "any", "base"], "win7-x64-aot": ["win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win8-x64": ["win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win8-x64-aot": ["win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win81-x64": ["win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win81-x64-aot": ["win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}