using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoClassTime.Models
{
    /// <summary>
    /// 排课记录实体类
    /// </summary>
    public class ClassRecord
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 学生ID
        /// </summary>
        [Required]
        public int StudentId { get; set; }

        /// <summary>
        /// 课程ID
        /// </summary>
        [Required]
        public int CourseId { get; set; }

        /// <summary>
        /// 时间槽ID
        /// </summary>
        [Required]
        public int TimeSlotId { get; set; }

        /// <summary>
        /// 第二个学生ID（双人课程）
        /// </summary>
        public int? SecondStudentId { get; set; }

        /// <summary>
        /// 课程状态
        /// </summary>
        [Required]
        public ClassStatus Status { get; set; } = ClassStatus.Scheduled;

        /// <summary>
        /// 实际开始时间
        /// </summary>
        public DateTime? ActualStartTime { get; set; }

        /// <summary>
        /// 实际结束时间
        /// </summary>
        public DateTime? ActualEndTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey("StudentId")]
        public virtual Student Student { get; set; }

        [ForeignKey("SecondStudentId")]
        public virtual Student SecondStudent { get; set; }

        [ForeignKey("CourseId")]
        public virtual Course Course { get; set; }

        [ForeignKey("TimeSlotId")]
        public virtual TimeSlot TimeSlot { get; set; }

        /// <summary>
        /// 是否为双人课程
        /// </summary>
        public bool IsPairClass => SecondStudentId.HasValue;
    }

    /// <summary>
    /// 课程状态枚举
    /// </summary>
    public enum ClassStatus
    {
        /// <summary>
        /// 已安排
        /// </summary>
        Scheduled = 0,

        /// <summary>
        /// 进行中
        /// </summary>
        InProgress = 1,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 2,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 3,

        /// <summary>
        /// 学生缺席
        /// </summary>
        StudentAbsent = 4
    }
}
