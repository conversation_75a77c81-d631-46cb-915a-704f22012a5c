# 私教排课系统 (AutoClassTime)

一个专为私教老师设计的简单易用的排课管理软件，支持学生管理、课程设置、时间安排和冲突检测等功能。

## 功能特点

### ✅ 已完成功能

1. **🤖 智能文本解析排课**
   - 自然语言文本解析（支持复杂排课描述）
   - 智能识别科目、时间、学生姓名
   - 自动约束条件分析和冲突检测
   - 最优排课算法和结果输出

2. **📊 数据模型设计**
   - 学生实体设计（姓名、联系方式、地址、备注）
   - 课程实体设计（课程名称、时长、价格、颜色标识）
   - 时间槽实体设计（日期、时间段、可用性）
   - 排课记录关联表（支持单人和双人课程）

3. **🧠 核心算法实现**
   - 时间冲突检测算法
   - 排课自动分配功能
   - 双人课程支持
   - 排课结果导出（CSV格式）

4. **💻 用户界面开发**
   - 主界面框架（极简设计，大按钮操作）
   - 智能文本解析排课界面（左右分栏，实时处理）
   - 学生管理模块（添加、编辑、删除、搜索）
   - 课程设置模块（课程管理和配置）
   - 排课日历视图（直观的时间表显示）
   - 课程恢复功能（支持恢复已取消的课程）

5. **💾 数据持久化**
   - SQLite数据库集成（本地存储，无需网络）
   - 数据备份机制（自动备份到用户文档目录）
   - 数据恢复功能（从备份文件恢复）

## 技术架构

- **框架**: .NET 5.0 WPF
- **数据库**: SQLite (Entity Framework Core)
- **架构模式**: MVVM
- **UI设计**: 极简风格，适合非技术用户

## 安装和运行

### 系统要求
- Windows 10 或更高版本
- .NET 5.0 Runtime

### 运行方式

1. **开发环境运行**
   ```bash
   dotnet run --project AutoClassTime
   ```

2. **发布版本**
   ```bash
   dotnet publish AutoClassTime -c Release -r win-x64 --self-contained
   ```

## 使用指南

### 首次使用

1. 启动应用程序，系统会自动初始化数据库
2. 默认会创建三门示例课程（数学辅导、英语辅导、语文辅导）
3. 点击"学生管理"开始添加学生信息

### 基本操作流程

1. **🤖 智能文本排课（推荐）**
   - 点击"智能排课"进入文本解析界面
   - 输入或粘贴排课文本（支持复杂的自然语言描述）
   - 点击"开始智能排课"自动生成最优方案
   - 查看按学生分组的详细排课结果

2. **👥 学生管理**
   - 点击"添加学生"按钮
   - 填写学生基本信息（姓名为必填项）
   - 保存学生信息

3. **⏰ 时间管理**
   - 点击"时间管理"进入时间槽设置
   - 选择日期，点击"生成本周时间槽"快速创建标准时间表
   - 或手动添加特定时间槽

4. **📅 排课管理**
   - 点击"排课管理"进入主要工作界面
   - 选择学生和课程
   - 点击"自动排课"让系统智能安排时间
   - 或手动选择时间槽进行排课
   - 支持取消和恢复课程功能

5. **💾 数据备份**
   - 点击"备份数据"按钮
   - 系统会自动将数据库备份到用户文档目录

## 数据存储位置

- **数据库文件**: `%APPDATA%\AutoClassTime\ClassTime.db`
- **备份文件**: `%APPDATA%\AutoClassTime\Backups\`
- **导出文件**: `%APPDATA%\AutoClassTime\Exports\`

## 设计原则

1. **界面极简**: 所有操作不超过3步点击
2. **数据安全**: 所有数据本地存储，不联网
3. **实时反馈**: 任何操作都有明确视觉反馈
4. **防错设计**: 防止用户误操作导致数据错误

## 常见问题解决

### TimeSpan排序错误修复

如果遇到"SQLite cannot order by expressions of type 'TimeSpan'"错误：

1. **自动修复**：应用程序会自动检测并提供修复选项
2. **手动修复**：删除 `%APPDATA%\AutoClassTime\ClassTime.db` 文件，重新启动应用
3. **数据备份**：修复前建议使用"备份数据"功能保存重要数据

### 启动问题

如果应用程序启动时出现错误：
1. 确保已安装 .NET 5.0 Runtime
2. 检查用户文档目录的写入权限
3. 尝试以管理员身份运行（仅限首次）

## 功能测试

运行内置测试来验证系统功能：

```csharp
// 在代码中调用
await TestRunner.RunAllTestsAsync();
```

测试包括：
- 数据库初始化测试
- 学生操作测试
- 课程操作测试
- 时间槽操作测试
- 冲突检测测试
- 排课功能测试
- 数据导出测试

## 开发进度

- [x] 1. 数据模型设计
  - [x] 学生实体设计
  - [x] 课程实体设计
  - [x] 时间槽实体设计
  - [x] 关联关系设计

- [x] 2. 核心算法实现
  - [x] 时间冲突检测
  - [x] 排课自动分配
  - [x] 双人课程支持
  - [x] 排课结果导出

- [x] 3. 用户界面开发
  - [x] 主界面框架
  - [x] 学生管理模块
  - [x] 课程设置模块
  - [x] 排课日历视图

- [x] 4. 数据持久化
  - [x] SQLite数据库集成
  - [x] 数据备份机制
  - [x] 数据恢复功能

- [x] 5. 测试和优化
  - [x] 功能测试
  - [x] 性能优化
  - [x] 稳定性确保

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址: [GitHub Repository]
- 邮箱: [Your Email]

---

**注意**: 这是一个离线软件，所有数据都存储在本地，请定期备份重要数据。
