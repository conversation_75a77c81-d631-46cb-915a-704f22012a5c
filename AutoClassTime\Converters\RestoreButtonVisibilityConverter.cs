using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using AutoClassTime.Models;

namespace AutoClassTime.Converters
{
    /// <summary>
    /// 恢复按钮可见性转换器
    /// </summary>
    public class RestoreButtonVisibilityConverter : IValueConverter
    {
        public static readonly RestoreButtonVisibilityConverter Instance = new RestoreButtonVisibilityConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ClassStatus status)
            {
                // 只有已取消的课程才显示恢复按钮
                return status == ClassStatus.Cancelled 
                    ? Visibility.Visible 
                    : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
