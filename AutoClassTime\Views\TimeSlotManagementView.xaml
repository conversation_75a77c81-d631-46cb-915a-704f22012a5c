<UserControl x:Class="AutoClassTime.Views.TimeSlotManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:AutoClassTime.Converters"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderBrush" Value="#bdc3c7"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5,5,5,2"/>
            <Setter Property="Foreground" Value="#2c3e50"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧：时间槽列表 -->
        <Border Grid.Column="0" Background="White" Margin="5"
                BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题 -->
                <TextBlock Grid.Row="0" Text="时间槽管理"
                          FontSize="18" FontWeight="Bold"
                          Foreground="#2c3e50" Margin="15,15,15,10"/>

                <!-- 日期选择和操作栏 -->
                <Border Grid.Row="1" Background="#f8f9fa" Margin="10,0,10,10"
                        BorderBrush="#dee2e6" BorderThickness="1" CornerRadius="3">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="选择日期:"
                                  VerticalAlignment="Center" Margin="10,0,5,0"
                                  FontWeight="Bold"/>

                        <DatePicker Grid.Column="1"
                                   SelectedDate="{Binding SelectedDate}"
                                   Margin="5" VerticalAlignment="Center"/>

                        <Button Grid.Column="2" Content="加载"
                               Style="{StaticResource ActionButtonStyle}"
                               Background="#3498db" Foreground="White"
                               Command="{Binding LoadDateCommand}"/>

                        <Button Grid.Column="3" Content="生成本周"
                               Style="{StaticResource ActionButtonStyle}"
                               Background="#27ae60" Foreground="White"
                               Command="{Binding GenerateWeekCommand}"/>
                    </Grid>
                </Border>

                <!-- 时间槽列表 -->
                <DataGrid Grid.Row="2"
                         ItemsSource="{Binding TimeSlots}"
                         SelectedItem="{Binding SelectedTimeSlot}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         CanUserReorderColumns="False"
                         CanUserResizeRows="False"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         Margin="10">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="日期" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="100"/>
                        <DataGridTextColumn Header="开始时间" Binding="{Binding StartTime, StringFormat=hh\\:mm}" Width="80"/>
                        <DataGridTextColumn Header="结束时间" Binding="{Binding EndTime, StringFormat=hh\\:mm}" Width="80"/>
                        <DataGridTextColumn Header="时长(分钟)" Binding="{Binding DurationMinutes}" Width="80"/>
                        <DataGridCheckBoxColumn Header="可用" Binding="{Binding IsAvailable}" Width="60"/>
                        <DataGridTextColumn Header="备注" Binding="{Binding Notes}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 操作按钮 -->
                <StackPanel Grid.Row="3" Orientation="Horizontal"
                           HorizontalAlignment="Center" Margin="10">
                    <Button Content="添加时间槽"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#27ae60" Foreground="White"
                           Command="{Binding AddTimeSlotCommand}"/>
                    <Button Content="编辑时间槽"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#f39c12" Foreground="White"
                           Command="{Binding EditTimeSlotCommand}"/>
                    <Button Content="删除时间槽"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#e74c3c" Foreground="White"
                           Command="{Binding DeleteTimeSlotCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 右侧：时间槽编辑 -->
        <Border Grid.Column="1" Background="White" Margin="5"
                BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
            <Border.Visibility>
                <Binding Path="IsEditing">
                    <Binding.Converter>
                        <BooleanToVisibilityConverter/>
                    </Binding.Converter>
                </Binding>
            </Border.Visibility>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 编辑标题 -->
                <TextBlock Grid.Row="0"
                          Text="{Binding EditingTimeSlot.Id, Converter={x:Static converters:IdToTitleConverter.Instance}}"
                          FontSize="16" FontWeight="Bold"
                          Foreground="#2c3e50" Margin="15,15,15,10"/>

                <!-- 编辑表单 -->
                <ScrollViewer Grid.Row="1" Margin="15,0">
                    <StackPanel>
                        <TextBlock Text="日期 *" Style="{StaticResource LabelStyle}"/>
                        <DatePicker SelectedDate="{Binding EditingTimeSlot.Date}"
                                   Margin="5" Padding="8"/>

                        <TextBlock Text="开始时间 *" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox Grid.Column="0" Margin="0,0,5,0"
                                     SelectedValue="{Binding StartHour}"
                                     SelectedValuePath="Content">
                                <ComboBoxItem Content="8"/>
                                <ComboBoxItem Content="9"/>
                                <ComboBoxItem Content="10"/>
                                <ComboBoxItem Content="11"/>
                                <ComboBoxItem Content="12"/>
                                <ComboBoxItem Content="13"/>
                                <ComboBoxItem Content="14"/>
                                <ComboBoxItem Content="15"/>
                                <ComboBoxItem Content="16"/>
                                <ComboBoxItem Content="17"/>
                                <ComboBoxItem Content="18"/>
                                <ComboBoxItem Content="19"/>
                                <ComboBoxItem Content="20"/>
                            </ComboBox>

                            <TextBlock Grid.Column="1" Text=":" VerticalAlignment="Center" Margin="5,0"/>

                            <ComboBox Grid.Column="2" Margin="5,0,0,0"
                                     SelectedValue="{Binding StartMinute}"
                                     SelectedValuePath="Content">
                                <ComboBoxItem Content="0"/>
                                <ComboBoxItem Content="15"/>
                                <ComboBoxItem Content="30"/>
                                <ComboBoxItem Content="45"/>
                            </ComboBox>
                        </Grid>

                        <TextBlock Text="结束时间 *" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox Grid.Column="0" Margin="0,0,5,0"
                                     SelectedValue="{Binding EndHour}"
                                     SelectedValuePath="Content">
                                <ComboBoxItem Content="9"/>
                                <ComboBoxItem Content="10"/>
                                <ComboBoxItem Content="11"/>
                                <ComboBoxItem Content="12"/>
                                <ComboBoxItem Content="13"/>
                                <ComboBoxItem Content="14"/>
                                <ComboBoxItem Content="15"/>
                                <ComboBoxItem Content="16"/>
                                <ComboBoxItem Content="17"/>
                                <ComboBoxItem Content="18"/>
                                <ComboBoxItem Content="19"/>
                                <ComboBoxItem Content="20"/>
                                <ComboBoxItem Content="21"/>
                            </ComboBox>

                            <TextBlock Grid.Column="1" Text=":" VerticalAlignment="Center" Margin="5,0"/>

                            <ComboBox Grid.Column="2" Margin="5,0,0,0"
                                     SelectedValue="{Binding EndMinute}"
                                     SelectedValuePath="Content">
                                <ComboBoxItem Content="0"/>
                                <ComboBoxItem Content="15"/>
                                <ComboBoxItem Content="30"/>
                                <ComboBoxItem Content="45"/>
                            </ComboBox>
                        </Grid>

                        <CheckBox Content="可用"
                                 IsChecked="{Binding EditingTimeSlot.IsAvailable}"
                                 Margin="5" FontSize="12"/>

                        <TextBlock Text="备注" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingTimeSlot.Notes, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"
                                Height="60" TextWrapping="Wrap"
                                AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- 保存/取消按钮 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal"
                           HorizontalAlignment="Center" Margin="15">
                    <Button Content="保存"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#27ae60" Foreground="White"
                           Command="{Binding SaveTimeSlotCommand}"/>
                    <Button Content="取消"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#95a5a6" Foreground="White"
                           Command="{Binding CancelEditCommand}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
