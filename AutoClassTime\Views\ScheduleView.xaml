<UserControl x:Class="AutoClassTime.Views.ScheduleView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:AutoClassTime.Converters"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1200">

    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5,5,5,2"/>
            <Setter Property="Foreground" Value="#2c3e50"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="排课管理"
                  FontSize="20" FontWeight="Bold"
                  Foreground="#2c3e50" Margin="15,15,15,10"/>

        <!-- 排课操作区 -->
        <Border Grid.Row="1" Background="#f8f9fa" Margin="10"
                BorderBrush="#dee2e6" BorderThickness="1" CornerRadius="5">
            <Grid Margin="15">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 第一行：日期选择和基本操作 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="选择日期:"
                              VerticalAlignment="Center" Margin="0,0,10,0"
                              Style="{StaticResource LabelStyle}"/>

                    <DatePicker Grid.Column="1"
                               SelectedDate="{Binding SelectedDate}"
                               Margin="0,0,10,0" VerticalAlignment="Center"/>

                    <Button Grid.Column="2" Content="加载当日课程"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#3498db" Foreground="White"
                           Command="{Binding LoadDateCommand}"/>

                    <Button Grid.Column="3" Content="刷新"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#95a5a6" Foreground="White"
                           Command="{Binding RefreshCommand}"/>
                </Grid>

                <!-- 第二行：排课操作 -->
                <Grid Grid.Row="1" Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="学生:"
                              VerticalAlignment="Center" Margin="0,0,5,0"
                              Style="{StaticResource LabelStyle}"/>

                    <ComboBox Grid.Column="1"
                             ItemsSource="{Binding Students}"
                             SelectedItem="{Binding SelectedStudent}"
                             DisplayMemberPath="Name"
                             Margin="0,0,10,0" Padding="8"/>

                    <TextBlock Grid.Column="2" Text="课程:"
                              VerticalAlignment="Center" Margin="0,0,5,0"
                              Style="{StaticResource LabelStyle}"/>

                    <ComboBox Grid.Column="3"
                             ItemsSource="{Binding Courses}"
                             SelectedItem="{Binding SelectedCourse}"
                             DisplayMemberPath="Name"
                             Margin="0,0,10,0" Padding="8"/>

                    <Button Grid.Column="4" Content="自动排课"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#27ae60" Foreground="White"
                           Command="{Binding AutoScheduleCommand}"/>

                    <Button Grid.Column="5" Content="手动排课"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#f39c12" Foreground="White"
                           Command="{Binding ScheduleClassCommand}"/>

                    <!-- 动态显示取消或恢复按钮 -->
                    <Grid Grid.Column="6">
                        <Button Content="取消课程"
                               Style="{StaticResource ActionButtonStyle}"
                               Background="#e74c3c" Foreground="White"
                               Command="{Binding CancelClassCommand}">
                            <Button.Visibility>
                                <Binding Path="SelectedClassRecord.Status" Converter="{x:Static converters:CancelButtonVisibilityConverter.Instance}"/>
                            </Button.Visibility>
                        </Button>

                        <Button Content="恢复课程"
                               Style="{StaticResource ActionButtonStyle}"
                               Background="#27ae60" Foreground="White"
                               Command="{Binding RestoreClassCommand}">
                            <Button.Visibility>
                                <Binding Path="SelectedClassRecord.Status" Converter="{x:Static converters:RestoreButtonVisibilityConverter.Instance}"/>
                            </Button.Visibility>
                        </Button>
                    </Grid>
                </Grid>
            </Grid>
        </Border>

        <!-- 主内容区 -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：当日课程安排 -->
            <Border Grid.Column="0" Background="White" Margin="0,0,5,0"
                    BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="当日课程安排"
                              FontSize="16" FontWeight="Bold"
                              Foreground="#2c3e50" Margin="15,15,15,10"/>

                    <DataGrid Grid.Row="1"
                             ItemsSource="{Binding ClassRecords}"
                             SelectedItem="{Binding SelectedClassRecord}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             CanUserReorderColumns="False"
                             CanUserResizeRows="False"
                             SelectionMode="Single"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             Margin="10">
                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Background" Value="{Binding Status, Converter={x:Static converters:StatusToBackgroundConverter.Instance}}"/>
                            </Style>
                        </DataGrid.RowStyle>
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="时间" Width="100">
                                <DataGridTextColumn.Binding>
                                    <MultiBinding StringFormat="{}{0:hh\:mm}-{1:hh\:mm}">
                                        <Binding Path="TimeSlot.StartTime"/>
                                        <Binding Path="TimeSlot.EndTime"/>
                                    </MultiBinding>
                                </DataGridTextColumn.Binding>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="学生" Binding="{Binding Student.Name}" Width="100"/>
                            <DataGridTextColumn Header="课程" Binding="{Binding Course.Name}" Width="100"/>
                            <DataGridTextColumn Header="状态" Width="80">
                                <DataGridTextColumn.Binding>
                                    <Binding Path="Status" Converter="{x:Static converters:StatusToTextConverter.Instance}"/>
                                </DataGridTextColumn.Binding>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="备注" Binding="{Binding Notes}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>

            <!-- 右侧：可用时间槽 -->
            <Border Grid.Column="1" Background="White" Margin="5,0,0,0"
                    BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="可用时间槽"
                              FontSize="16" FontWeight="Bold"
                              Foreground="#2c3e50" Margin="15,15,15,10"/>

                    <ListBox Grid.Row="1"
                            ItemsSource="{Binding AvailableTimeSlots}"
                            SelectedItem="{Binding SelectedTimeSlot}"
                            Margin="10">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#ecf0f1" Margin="2" Padding="8" CornerRadius="3">
                                    <StackPanel>
                                        <TextBlock Text="{Binding Date, StringFormat=yyyy-MM-dd}"
                                                  FontWeight="Bold" FontSize="11"/>
                                        <TextBlock FontSize="14" FontWeight="Bold">
                                            <TextBlock.Text>
                                                <MultiBinding StringFormat="{}{0:hh\:mm} - {1:hh\:mm}">
                                                    <Binding Path="StartTime"/>
                                                    <Binding Path="EndTime"/>
                                                </MultiBinding>
                                            </TextBlock.Text>
                                        </TextBlock>
                                        <TextBlock Text="{Binding DurationMinutes, StringFormat={}{0}分钟}"
                                                  FontSize="10" Foreground="Gray"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <Button Grid.Row="2" Content="确认排课到选中时间"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#27ae60" Foreground="White"
                           Margin="10"
                           Command="{Binding RescheduleClassCommand}"/>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
