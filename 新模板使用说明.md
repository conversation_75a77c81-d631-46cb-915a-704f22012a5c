# 智能排课新模板使用说明

## 🎯 模板升级完成

我已经成功为您创建了全新的智能排课文本模板，并完全重构了解析系统来支持这个标准化格式。

## 📋 新模板格式

### 标准模板结构
```
教师资源:
[科目][教师姓名]: [时间安排]

学生需求:
学生[姓名]: 需要上 [数量] 节[科目]课，[数量] 节[科目]课。

[学生姓名]的特殊要求: [约束条件]

排课规则:
[通用规则说明]

任务: [排课目标]
```

### 您提供的示例对应的新格式
```
教师资源:

语文吴老师: 周六 10:00-12:00
数学郑老师: 周日 13:00-15:00, 15:00-17:00
物理冯老师: 周六 13:00-15:00 (可拼班), 周日 10:00-12:00

学生需求:

学生大强: 需要上 1 节物理课，1 节数学课。
学生小雨: 需要上 1 节物理课，1 节语文课。

大强的特殊要求: 不想在周日上课。
小雨的特殊要求: 希望和 大强 一起上物理课。

排课规则:

所有课程时长均为2小时。

任务: 为大强和小雨安排所有课程。
```

## 🔧 新功能特点

### 1. 结构化解析
- **章节识别**: 自动识别"教师资源:"、"学生需求:"等章节
- **格式统一**: 每个章节有固定的格式规范
- **错误容错**: 更好的错误处理和提示

### 2. 教师资源解析
- **格式**: `科目教师姓名: 周X HH:MM-HH:MM`
- **多时间段**: 支持逗号分隔的多个时间段
- **拼班标识**: 支持`(可拼班)`标注
- **教师信息**: 自动提取科目和教师姓名

### 3. 学生需求解析
- **格式**: `学生姓名: 需要上 X 节科目课，Y 节科目课。`
- **多科目**: 支持一行内多个科目需求
- **数量识别**: 自动识别课程数量

### 4. 特殊要求解析
- **时间限制**: "不想在周日上课"、"只能在周六上课"
- **拼班要求**: "希望和 学生名 一起上科目课"
- **时间偏好**: "希望在上午上课"等

### 5. 智能约束处理
- **约束分类**: 自动分类为时间限制、拼班要求、时间偏好等
- **冲突检测**: 智能检测约束冲突
- **优先级处理**: 优先满足特殊要求

## 🚀 使用方法

### 第一步：启动应用程序
```bash
dotnet run --project AutoClassTime
```

### 第二步：加载新模板
1. 应用程序启动后会显示智能排课界面
2. 点击"📝 加载示例"按钮
3. 查看左侧显示的新标准模板

### 第三步：使用您的数据
1. 将您的排课需求按照新模板格式整理
2. 替换示例中的教师、学生、时间信息
3. 添加您的特殊要求

### 第四步：开始智能排课
1. 点击"🚀 开始智能排课"按钮
2. 查看右侧的解析统计信息
3. 查看详细的排课结果

## 📊 解析能力

### 支持的时间格式
- **标准格式**: 周六 10:00-12:00
- **多时间段**: 周日 13:00-15:00, 15:00-17:00
- **拼班标识**: 周六 13:00-15:00 (可拼班)

### 支持的科目
- 语文、数学、英语、物理、化学、道法
- 可以轻松扩展其他科目

### 支持的约束类型
- **时间限制**: 不想在某天上课、只能在某天上课
- **拼班要求**: 希望和某学生一起上某科课
- **时间偏好**: 希望在某时间段上课

## 🎯 模板优势

### 1. 标准化格式
- 结构清晰，易于理解和填写
- 格式统一，减少解析错误
- 章节分明，便于维护和扩展

### 2. 智能解析
- 自动识别教师、学生、时间信息
- 智能提取约束条件
- 准确理解拼班需求

### 3. 灵活扩展
- 支持添加新的科目和教师
- 支持复杂的约束条件
- 支持多样化的时间安排

### 4. 用户友好
- 自然语言描述
- 直观的格式设计
- 清晰的结果输出

## 📝 填写指南

### 教师资源部分
```
教师资源:

语文吴老师: 周六 10:00-12:00
数学郑老师: 周日 13:00-15:00, 15:00-17:00
物理冯老师: 周六 13:00-15:00 (可拼班), 周日 10:00-12:00
```

**注意事项**:
- 科目名称要准确（语文、数学、英语、物理、化学、道法）
- 教师姓名要唯一
- 时间格式：周X HH:MM-HH:MM
- 多个时间段用逗号分隔
- 可拼班的时间段加上 (可拼班) 标识

### 学生需求部分
```
学生需求:

学生大强: 需要上 1 节物理课，1 节数学课。
学生小雨: 需要上 1 节物理课，1 节语文课。
```

**注意事项**:
- 学生姓名要唯一
- 课程数量要明确
- 科目名称要与教师资源中的一致

### 特殊要求部分
```
大强的特殊要求: 不想在周日上课。
小雨的特殊要求: 希望和 大强 一起上物理课。
```

**注意事项**:
- 学生姓名要与需求部分一致
- 约束描述要清晰明确
- 拼班要求要指定具体学生和科目

## 🎉 升级完成

新的智能排课模板系统已经完全实现并可以使用：

✅ **标准化模板**: 结构清晰的文本格式  
✅ **智能解析**: 准确理解各种需求和约束  
✅ **拼班支持**: 完整的拼班功能  
✅ **约束处理**: 智能处理特殊要求  
✅ **用户友好**: 直观的操作界面  

现在您可以使用这个标准化的模板格式来进行智能排课了！🚀
