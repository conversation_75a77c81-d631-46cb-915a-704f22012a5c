using Microsoft.EntityFrameworkCore;
using AutoClassTime.Data;
using AutoClassTime.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace AutoClassTime.ViewModels
{
    /// <summary>
    /// 时间槽管理ViewModel
    /// </summary>
    public class TimeSlotManagementViewModel : ViewModelBase
    {
        private readonly ClassTimeDbContext _context;
        private ObservableCollection<TimeSlot> _timeSlots;
        private TimeSlot _selectedTimeSlot;
        private TimeSlot _editingTimeSlot;
        private DateTime _selectedDate = DateTime.Today;
        private bool _isEditing;
        private int _startHour = 9;
        private int _startMinute = 0;
        private int _endHour = 10;
        private int _endMinute = 0;

        public TimeSlotManagementViewModel(ClassTimeDbContext context)
        {
            _context = context;
            TimeSlots = new ObservableCollection<TimeSlot>();

            // 初始化命令
            AddTimeSlotCommand = new RelayCommand(AddTimeSlot);
            EditTimeSlotCommand = new RelayCommand(EditTimeSlot, () => SelectedTimeSlot != null);
            DeleteTimeSlotCommand = new RelayCommand(async () => await DeleteTimeSlotAsync(), () => SelectedTimeSlot != null);
            SaveTimeSlotCommand = new RelayCommand(async () => await SaveTimeSlotAsync(), () => IsEditing);
            CancelEditCommand = new RelayCommand(CancelEdit, () => IsEditing);
            LoadDateCommand = new RelayCommand(async () => await LoadTimeSlotsAsync());
            GenerateWeekCommand = new RelayCommand(async () => await GenerateWeekTimeSlotsAsync());

            // 加载时间槽数据
            _ = LoadTimeSlotsAsync();
        }

        #region 属性

        public ObservableCollection<TimeSlot> TimeSlots
        {
            get => _timeSlots;
            set => SetProperty(ref _timeSlots, value);
        }

        public TimeSlot SelectedTimeSlot
        {
            get => _selectedTimeSlot;
            set
            {
                SetProperty(ref _selectedTimeSlot, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public TimeSlot EditingTimeSlot
        {
            get => _editingTimeSlot;
            set => SetProperty(ref _editingTimeSlot, value);
        }

        public DateTime SelectedDate
        {
            get => _selectedDate;
            set => SetProperty(ref _selectedDate, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set
            {
                SetProperty(ref _isEditing, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        /// <summary>
        /// 开始小时
        /// </summary>
        public int StartHour
        {
            get => _startHour;
            set => SetProperty(ref _startHour, value);
        }

        /// <summary>
        /// 开始分钟
        /// </summary>
        public int StartMinute
        {
            get => _startMinute;
            set => SetProperty(ref _startMinute, value);
        }

        /// <summary>
        /// 结束小时
        /// </summary>
        public int EndHour
        {
            get => _endHour;
            set => SetProperty(ref _endHour, value);
        }

        /// <summary>
        /// 结束分钟
        /// </summary>
        public int EndMinute
        {
            get => _endMinute;
            set => SetProperty(ref _endMinute, value);
        }

        #endregion

        #region 命令

        public ICommand AddTimeSlotCommand { get; }
        public ICommand EditTimeSlotCommand { get; }
        public ICommand DeleteTimeSlotCommand { get; }
        public ICommand SaveTimeSlotCommand { get; }
        public ICommand CancelEditCommand { get; }
        public ICommand LoadDateCommand { get; }
        public ICommand GenerateWeekCommand { get; }

        #endregion

        #region 方法

        private async Task LoadTimeSlotsAsync()
        {
            try
            {
                var timeSlots = await _context.TimeSlots
                    .Where(ts => ts.Date.Date == SelectedDate.Date)
                    .ToListAsync();

                // 在内存中排序，避免SQLite的TimeSpan排序问题
                var sortedTimeSlots = timeSlots.OrderBy(ts => ts.StartTime).ToList();

                TimeSlots.Clear();
                foreach (var timeSlot in sortedTimeSlots)
                {
                    TimeSlots.Add(timeSlot);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载时间槽数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddTimeSlot()
        {
            EditingTimeSlot = new TimeSlot
            {
                Date = SelectedDate,
                StartTime = new TimeSpan(9, 0, 0),
                EndTime = new TimeSpan(10, 0, 0),
                IsAvailable = true
            };
            StartHour = 9;
            StartMinute = 0;
            EndHour = 10;
            EndMinute = 0;
            IsEditing = true;
        }

        private void EditTimeSlot()
        {
            if (SelectedTimeSlot == null) return;

            EditingTimeSlot = new TimeSlot
            {
                Id = SelectedTimeSlot.Id,
                Date = SelectedTimeSlot.Date,
                StartTime = SelectedTimeSlot.StartTime,
                EndTime = SelectedTimeSlot.EndTime,
                IsAvailable = SelectedTimeSlot.IsAvailable,
                Notes = SelectedTimeSlot.Notes,
                CreatedAt = SelectedTimeSlot.CreatedAt,
                UpdatedAt = SelectedTimeSlot.UpdatedAt
            };
            StartHour = SelectedTimeSlot.StartTime.Hours;
            StartMinute = SelectedTimeSlot.StartTime.Minutes;
            EndHour = SelectedTimeSlot.EndTime.Hours;
            EndMinute = SelectedTimeSlot.EndTime.Minutes;
            IsEditing = true;
        }

        private async Task SaveTimeSlotAsync()
        {
            try
            {
                if (EditingTimeSlot == null) return;

                // 从UI控件更新时间
                EditingTimeSlot.StartTime = new TimeSpan(StartHour, StartMinute, 0);
                EditingTimeSlot.EndTime = new TimeSpan(EndHour, EndMinute, 0);

                // 验证时间
                if (EditingTimeSlot.StartTime >= EditingTimeSlot.EndTime)
                {
                    MessageBox.Show("开始时间必须早于结束时间", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 检查时间冲突
                var conflictingTimeSlot = await _context.TimeSlots
                    .Where(ts => ts.Id != EditingTimeSlot.Id &&
                                ts.Date.Date == EditingTimeSlot.Date.Date &&
                                ((ts.StartTime < EditingTimeSlot.EndTime && ts.EndTime > EditingTimeSlot.StartTime)))
                    .FirstOrDefaultAsync();

                if (conflictingTimeSlot != null)
                {
                    MessageBox.Show("该时间段与现有时间槽冲突", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (EditingTimeSlot.Id == 0)
                {
                    // 新增时间槽
                    EditingTimeSlot.CreatedAt = DateTime.Now;
                    EditingTimeSlot.UpdatedAt = DateTime.Now;
                    _context.TimeSlots.Add(EditingTimeSlot);
                }
                else
                {
                    // 更新时间槽
                    var existingTimeSlot = await _context.TimeSlots.FindAsync(EditingTimeSlot.Id);
                    if (existingTimeSlot != null)
                    {
                        existingTimeSlot.Date = EditingTimeSlot.Date;
                        existingTimeSlot.StartTime = EditingTimeSlot.StartTime;
                        existingTimeSlot.EndTime = EditingTimeSlot.EndTime;
                        existingTimeSlot.IsAvailable = EditingTimeSlot.IsAvailable;
                        existingTimeSlot.Notes = EditingTimeSlot.Notes;
                        existingTimeSlot.UpdatedAt = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();
                await LoadTimeSlotsAsync();

                IsEditing = false;
                EditingTimeSlot = null;

                MessageBox.Show("时间槽保存成功", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存时间槽失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelEdit()
        {
            IsEditing = false;
            EditingTimeSlot = null;
        }

        private async Task DeleteTimeSlotAsync()
        {
            try
            {
                if (SelectedTimeSlot == null) return;

                // 检查是否有课程安排
                var hasClasses = await _context.ClassRecords
                    .AnyAsync(cr => cr.TimeSlotId == SelectedTimeSlot.Id && cr.Status != ClassStatus.Cancelled);

                if (hasClasses)
                {
                    MessageBox.Show("该时间槽已有课程安排，无法删除", "删除失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"确定要删除时间槽 '{SelectedTimeSlot.StartTime:hh\\:mm}-{SelectedTimeSlot.EndTime:hh\\:mm}' 吗？",
                    "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                _context.TimeSlots.Remove(SelectedTimeSlot);
                await _context.SaveChangesAsync();
                await LoadTimeSlotsAsync();

                SelectedTimeSlot = null;
                MessageBox.Show("时间槽删除成功", "删除成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除时间槽失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task GenerateWeekTimeSlotsAsync()
        {
            try
            {
                var result = MessageBox.Show("确定要为本周生成标准时间槽吗？\n将生成周一到周日，每天9:00-18:00，每小时一个时间槽。",
                    "确认生成", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                var startOfWeek = SelectedDate.AddDays(-(int)SelectedDate.DayOfWeek + 1); // 周一
                var timeSlots = new List<TimeSlot>();

                for (int day = 0; day < 7; day++)
                {
                    var currentDate = startOfWeek.AddDays(day);

                    // 检查该日期是否已有时间槽
                    var existingCount = await _context.TimeSlots
                        .CountAsync(ts => ts.Date.Date == currentDate.Date);

                    if (existingCount > 0) continue;

                    for (int hour = 9; hour < 18; hour++)
                    {
                        var timeSlot = new TimeSlot
                        {
                            Date = currentDate,
                            StartTime = new TimeSpan(hour, 0, 0),
                            EndTime = new TimeSpan(hour + 1, 0, 0),
                            IsAvailable = true,
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };
                        timeSlots.Add(timeSlot);
                    }
                }

                if (timeSlots.Any())
                {
                    _context.TimeSlots.AddRange(timeSlots);
                    await _context.SaveChangesAsync();
                    await LoadTimeSlotsAsync();

                    MessageBox.Show($"成功生成 {timeSlots.Count()} 个时间槽", "生成成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("本周已有时间槽，无需重复生成", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成时间槽失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }
}
