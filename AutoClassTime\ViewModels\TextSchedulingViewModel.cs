using AutoClassTime.Models;
using AutoClassTime.Services;
using System;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace AutoClassTime.ViewModels
{
    /// <summary>
    /// 文本解析排课ViewModel
    /// </summary>
    public class TextSchedulingViewModel : ViewModelBase
    {
        private readonly TextParsingService _textParsingService;
        private string _inputText;
        private string _outputText;
        private bool _isProcessing;

        public TextSchedulingViewModel()
        {
            try
            {
                _textParsingService = new TextParsingService();

                // 初始化命令
                ParseAndScheduleCommand = new RelayCommand(async () => await ParseAndScheduleAsync(), () => !IsProcessing && !string.IsNullOrWhiteSpace(InputText));
                ClearInputCommand = new RelayCommand(ClearInput);
                ClearOutputCommand = new RelayCommand(ClearOutput);
                LoadSampleCommand = new RelayCommand(LoadSample);

                // 设置示例文本
                LoadSample();
            }
            catch (Exception ex)
            {
                OutputText = $"初始化失败: {ex.Message}\n\n请检查系统配置或联系技术支持。";
                MessageBox.Show($"TextSchedulingViewModel初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region 属性

        /// <summary>
        /// 输入文本
        /// </summary>
        public string InputText
        {
            get => _inputText;
            set
            {
                SetProperty(ref _inputText, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        /// <summary>
        /// 输出文本
        /// </summary>
        public string OutputText
        {
            get => _outputText;
            set => SetProperty(ref _outputText, value);
        }

        /// <summary>
        /// 是否正在处理
        /// </summary>
        public bool IsProcessing
        {
            get => _isProcessing;
            set
            {
                SetProperty(ref _isProcessing, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        #endregion

        #region 命令

        public ICommand ParseAndScheduleCommand { get; }
        public ICommand ClearInputCommand { get; }
        public ICommand ClearOutputCommand { get; }
        public ICommand LoadSampleCommand { get; }

        #endregion

        #region 方法

        /// <summary>
        /// 解析并排课
        /// </summary>
        private async System.Threading.Tasks.Task ParseAndScheduleAsync()
        {
            try
            {
                IsProcessing = true;
                OutputText = "正在解析排课文本，请稍候...";

                // 模拟异步处理
                await System.Threading.Tasks.Task.Delay(500);

                // 解析文本
                var parsedData = _textParsingService.ParseScheduleText(InputText);

                if (!parsedData.Success)
                {
                    OutputText = $"解析失败：{parsedData.ErrorMessage}";
                    return;
                }

                // 显示解析结果
                OutputText = $"✅ 文本解析成功！\n" +
                           $"📊 解析统计：\n" +
                           $"   - 时间槽数量: {parsedData.TimeSlots.Count}\n" +
                           $"   - 学生需求数量: {parsedData.StudentRequirements.Count}\n" +
                           $"   - 约束条件数量: {parsedData.StudentConstraints.Count}\n\n" +
                           $"正在生成排课安排...";

                await System.Threading.Tasks.Task.Delay(500);

                // 生成排课
                var scheduleResult = _textParsingService.GenerateSchedule(parsedData);

                if (!scheduleResult.Success)
                {
                    OutputText = $"排课生成失败：{scheduleResult.ErrorMessage}";
                    return;
                }

                // 格式化输出
                var formattedOutput = _textParsingService.FormatScheduleOutput(scheduleResult);

                // 添加统计信息
                var totalClasses = scheduleResult.StudentSchedules.Values.Sum(classes => classes.Count);
                var statsInfo = $"✅ 排课生成成功！\n" +
                              $"📊 排课统计：\n" +
                              $"   - 安排学生: {scheduleResult.StudentSchedules.Count} 人\n" +
                              $"   - 总课程数: {totalClasses} 节\n" +
                              $"   - 警告数量: {scheduleResult.Warnings.Count}\n\n";

                OutputText = statsInfo + formattedOutput;

                MessageBox.Show("智能排课完成！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                OutputText = $"处理过程中发生错误：{ex.Message}";
                MessageBox.Show($"处理失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// 清空输入
        /// </summary>
        private void ClearInput()
        {
            InputText = "";
        }

        /// <summary>
        /// 清空输出
        /// </summary>
        private void ClearOutput()
        {
            OutputText = "";
        }

        /// <summary>
        /// 加载示例文本（新测试用例）
        /// </summary>
        private void LoadSample()
        {
            InputText = @"教师资源:

数学陈老师: 周六 9:00-11:00, 周日 14:00-16:00
物理周老师: 周六 14:00-16:00
英语林老师: 周日 9:00-11:00

学生需求:

学生小芳: 需要上 2 节数学课, 1 节物理课, 1 节英语课。

排课规则:

所有课程时长均为2小时。
同一天内，同一个学生不能上同一门科目。

任务: 为小芳安排所有课程。";

            OutputText = "📋 已加载标准排课模板！\n\n" +
                        "🎯 新模板特点：\n" +
                        "• 结构化格式：教师资源 → 学生需求 → 特殊要求 → 排课规则\n" +
                        "• 支持拼班：标注(可拼班)的时间段可以安排多个学生\n" +
                        "• 智能约束：支持时间限制、拼班要求等特殊需求\n" +
                        "• 周时间格式：使用周一到周日的时间表示\n\n" +
                        "🚀 点击\"开始智能排课\"按钮开始解析和排课！\n\n" +
                        "系统将自动：\n" +
                        "1. 解析教师的可用时间和拼班设置\n" +
                        "2. 识别学生的课程需求\n" +
                        "3. 分析特殊要求和约束条件\n" +
                        "4. 智能生成最优排课方案\n" +
                        "5. 按学生分组输出详细结果";
        }

        #endregion
    }
}
