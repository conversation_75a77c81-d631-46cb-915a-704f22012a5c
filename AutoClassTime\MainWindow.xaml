﻿<Window x:Class="AutoClassTime.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AutoClassTime.Views"
        xmlns:vm="clr-namespace:AutoClassTime.ViewModels"
        mc:Ignorable="d"
        Title="私教排课系统" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        MinHeight="600" MinWidth="900">

    <Window.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#3498db"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980b9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1f5f8b"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 标题样式 -->
        <Style x:Key="TitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2c3e50"/>
            <Setter Property="Margin" Value="10"/>
        </Style>

        <!-- 状态栏样式 -->
        <Style x:Key="StatusBarStyle" TargetType="StatusBar">
            <Setter Property="Background" Value="#ecf0f1"/>
            <Setter Property="BorderBrush" Value="#bdc3c7"/>
            <Setter Property="BorderThickness" Value="0,1,0,0"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#34495e" Padding="10">
            <TextBlock Text="私教排课系统"
                      Style="{StaticResource TitleStyle}"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- 菜单栏 -->
        <Border Grid.Row="1" Background="#ecf0f1" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="智能排课"
                       Style="{StaticResource MenuButtonStyle}"
                       Background="#9b59b6" Foreground="White"
                       Command="{Binding ShowTextSchedulingCommand}"/>
                <Button Content="排课管理"
                       Style="{StaticResource MenuButtonStyle}"
                       Command="{Binding ShowScheduleViewCommand}"/>
                <Button Content="学生管理"
                       Style="{StaticResource MenuButtonStyle}"
                       Command="{Binding ShowStudentManagementCommand}"/>
                <Button Content="课程管理"
                       Style="{StaticResource MenuButtonStyle}"
                       Command="{Binding ShowCourseManagementCommand}"/>
                <Button Content="时间管理"
                       Style="{StaticResource MenuButtonStyle}"
                       Command="{Binding ShowTimeSlotManagementCommand}"/>
                <Button Content="备份数据"
                       Style="{StaticResource MenuButtonStyle}"
                       Background="#27ae60"
                       Command="{Binding BackupDatabaseCommand}"/>
                <Button Content="退出"
                       Style="{StaticResource MenuButtonStyle}"
                       Background="#e74c3c"
                       Command="{Binding ExitApplicationCommand}"/>
            </StackPanel>
        </Border>

        <!-- 主内容区域 -->
        <Border Grid.Row="2" Background="White" Margin="10">
            <ContentControl Content="{Binding CurrentViewModel}">
                <ContentControl.Resources>
                    <!-- 文本解析排课视图 -->
                    <DataTemplate DataType="{x:Type vm:TextSchedulingViewModel}">
                        <local:TextSchedulingView/>
                    </DataTemplate>

                    <!-- 学生管理视图 -->
                    <DataTemplate DataType="{x:Type vm:StudentManagementViewModel}">
                        <local:StudentManagementView/>
                    </DataTemplate>

                    <!-- 课程管理视图 -->
                    <DataTemplate DataType="{x:Type vm:CourseManagementViewModel}">
                        <local:CourseManagementView/>
                    </DataTemplate>

                    <!-- 时间槽管理视图 -->
                    <DataTemplate DataType="{x:Type vm:TimeSlotManagementViewModel}">
                        <local:TimeSlotManagementView/>
                    </DataTemplate>

                    <!-- 排课管理视图 -->
                    <DataTemplate DataType="{x:Type vm:ScheduleViewModel}">
                        <local:ScheduleView/>
                    </DataTemplate>
                </ContentControl.Resources>
            </ContentControl>
        </Border>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="3" Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{Binding StatusMessage}" Margin="5,0"/>
                    <ProgressBar Width="100" Height="16" Margin="10,0"
                               IsIndeterminate="{Binding IsLoading}">
                        <ProgressBar.Visibility>
                            <Binding Path="IsLoading">
                                <Binding.Converter>
                                    <BooleanToVisibilityConverter/>
                                </Binding.Converter>
                            </Binding>
                        </ProgressBar.Visibility>
                    </ProgressBar>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
