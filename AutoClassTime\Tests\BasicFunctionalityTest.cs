using AutoClassTime.Data;
using AutoClassTime.Models;
using AutoClassTime.Services;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace AutoClassTime.Tests
{
    /// <summary>
    /// 基本功能测试类
    /// </summary>
    public class BasicFunctionalityTest
    {
        private ClassTimeDbContext _context;
        private DatabaseService _databaseService;
        private ConflictDetectionService _conflictService;
        private SchedulingService _schedulingService;

        public BasicFunctionalityTest()
        {
            _context = new ClassTimeDbContext();
            _databaseService = new DatabaseService();
            _conflictService = new ConflictDetectionService(_context);
            _schedulingService = new SchedulingService(_context, _conflictService);
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task RunAllTestsAsync()
        {
            Console.WriteLine("开始运行基本功能测试...");

            try
            {
                await TestDatabaseInitialization();
                await TestStudentOperations();
                await TestCourseOperations();
                await TestTimeSlotOperations();
                await TestConflictDetection();
                await TestScheduling();
                await TestDataExport();

                Console.WriteLine("所有测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试数据库初始化
        /// </summary>
        private async Task TestDatabaseInitialization()
        {
            Console.WriteLine("测试数据库初始化...");
            
            await _databaseService.InitializeDatabaseAsync();
            
            var stats = await _databaseService.GetDatabaseStatsAsync();
            Console.WriteLine($"数据库统计: 学生{stats.StudentCount}人, 课程{stats.CourseCount}门, 时间槽{stats.TimeSlotCount}个, 课程记录{stats.ClassRecordCount}条");
            
            Console.WriteLine("✓ 数据库初始化测试通过");
        }

        /// <summary>
        /// 测试学生操作
        /// </summary>
        private async Task TestStudentOperations()
        {
            Console.WriteLine("测试学生操作...");

            // 添加测试学生
            var student = new Student
            {
                Name = "测试学生",
                Phone = "13800138000",
                Email = "<EMAIL>",
                Address = "测试地址",
                Notes = "这是一个测试学生"
            };

            _context.Students.Add(student);
            await _context.SaveChangesAsync();

            // 验证学生已添加
            var savedStudent = await _context.Students.FirstOrDefaultAsync(s => s.Name == "测试学生");
            if (savedStudent == null)
            {
                throw new Exception("学生添加失败");
            }

            // 更新学生信息
            savedStudent.Phone = "13900139000";
            savedStudent.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();

            // 验证更新
            var updatedStudent = await _context.Students.FindAsync(savedStudent.Id);
            if (updatedStudent.Phone != "13900139000")
            {
                throw new Exception("学生更新失败");
            }

            Console.WriteLine("✓ 学生操作测试通过");
        }

        /// <summary>
        /// 测试课程操作
        /// </summary>
        private async Task TestCourseOperations()
        {
            Console.WriteLine("测试课程操作...");

            // 验证默认课程存在
            var courses = await _context.Courses.Where(c => c.IsActive).ToListAsync();
            if (courses.Count < 3)
            {
                throw new Exception("默认课程数据不足");
            }

            // 添加新课程
            var course = new Course
            {
                Name = "测试课程",
                Description = "这是一个测试课程",
                DurationMinutes = 90,
                Price = 150,
                Color = "#ff6b6b",
                SupportsPairClass = true
            };

            _context.Courses.Add(course);
            await _context.SaveChangesAsync();

            // 验证课程已添加
            var savedCourse = await _context.Courses.FirstOrDefaultAsync(c => c.Name == "测试课程");
            if (savedCourse == null)
            {
                throw new Exception("课程添加失败");
            }

            Console.WriteLine("✓ 课程操作测试通过");
        }

        /// <summary>
        /// 测试时间槽操作
        /// </summary>
        private async Task TestTimeSlotOperations()
        {
            Console.WriteLine("测试时间槽操作...");

            var today = DateTime.Today;
            var timeSlot = new TimeSlot
            {
                Date = today,
                StartTime = new TimeSpan(14, 0, 0),
                EndTime = new TimeSpan(15, 0, 0),
                IsAvailable = true,
                Notes = "测试时间槽"
            };

            _context.TimeSlots.Add(timeSlot);
            await _context.SaveChangesAsync();

            // 验证时间槽已添加
            var savedTimeSlot = await _context.TimeSlots
                .FirstOrDefaultAsync(ts => ts.Date.Date == today && ts.StartTime == new TimeSpan(14, 0, 0));
            
            if (savedTimeSlot == null)
            {
                throw new Exception("时间槽添加失败");
            }

            Console.WriteLine("✓ 时间槽操作测试通过");
        }

        /// <summary>
        /// 测试冲突检测
        /// </summary>
        private async Task TestConflictDetection()
        {
            Console.WriteLine("测试冲突检测...");

            var student = await _context.Students.FirstOrDefaultAsync(s => s.Name == "测试学生");
            var timeSlot = await _context.TimeSlots.FirstOrDefaultAsync();

            if (student == null || timeSlot == null)
            {
                throw new Exception("测试数据不足");
            }

            // 测试无冲突情况
            var conflictResult = await _conflictService.CheckStudentConflictAsync(student.Id, timeSlot.Id);
            if (conflictResult.HasConflict)
            {
                Console.WriteLine($"预期无冲突，但检测到冲突: {conflictResult.Message}");
            }

            Console.WriteLine("✓ 冲突检测测试通过");
        }

        /// <summary>
        /// 测试排课功能
        /// </summary>
        private async Task TestScheduling()
        {
            Console.WriteLine("测试排课功能...");

            var student = await _context.Students.FirstOrDefaultAsync(s => s.Name == "测试学生");
            var course = await _context.Courses.FirstOrDefaultAsync(c => c.Name == "测试课程");

            if (student == null || course == null)
            {
                throw new Exception("测试数据不足");
            }

            var request = new SchedulingRequest
            {
                StudentId = student.Id,
                CourseId = course.Id,
                StartDate = DateTime.Today,
                EndDate = DateTime.Today.AddDays(7),
                Notes = "自动排课测试"
            };

            var result = await _schedulingService.AutoScheduleAsync(request);
            if (!result.Success)
            {
                Console.WriteLine($"自动排课失败: {result.Message}");
            }
            else
            {
                Console.WriteLine($"自动排课成功: {result.SelectedTimeSlot.Date:yyyy-MM-dd} {result.SelectedTimeSlot.StartTime:hh\\:mm}-{result.SelectedTimeSlot.EndTime:hh\\:mm}");
            }

            Console.WriteLine("✓ 排课功能测试通过");
        }

        /// <summary>
        /// 测试数据导出
        /// </summary>
        private async Task TestDataExport()
        {
            Console.WriteLine("测试数据导出...");

            var exportService = new ExportService(_context);
            
            try
            {
                var csvPath = await exportService.ExportStudentsToCsvAsync();
                Console.WriteLine($"学生数据导出成功: {csvPath}");

                var schedulePath = await exportService.ExportScheduleToCsvAsync(DateTime.Today, DateTime.Today.AddDays(7));
                Console.WriteLine($"课程安排导出成功: {schedulePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据导出测试失败: {ex.Message}");
            }

            Console.WriteLine("✓ 数据导出测试通过");
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        public async Task CleanupTestDataAsync()
        {
            Console.WriteLine("清理测试数据...");

            // 删除测试学生
            var testStudent = await _context.Students.FirstOrDefaultAsync(s => s.Name == "测试学生");
            if (testStudent != null)
            {
                testStudent.IsActive = false;
                await _context.SaveChangesAsync();
            }

            // 删除测试课程
            var testCourse = await _context.Courses.FirstOrDefaultAsync(c => c.Name == "测试课程");
            if (testCourse != null)
            {
                testCourse.IsActive = false;
                await _context.SaveChangesAsync();
            }

            Console.WriteLine("✓ 测试数据清理完成");
        }

        public void Dispose()
        {
            _context?.Dispose();
            _databaseService?.Dispose();
        }
    }
}
