<UserControl x:Class="AutoClassTime.Views.CourseManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:converters="clr-namespace:AutoClassTime.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderBrush" Value="#bdc3c7"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5,5,5,2"/>
            <Setter Property="Foreground" Value="#2c3e50"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧：课程列表 -->
        <Border Grid.Column="0" Background="White" Margin="5" 
                BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题 -->
                <TextBlock Grid.Row="0" Text="课程管理" 
                          FontSize="18" FontWeight="Bold" 
                          Foreground="#2c3e50" Margin="15,15,15,10"/>

                <!-- 搜索栏 -->
                <Border Grid.Row="1" Background="#f8f9fa" Margin="10,0,10,10" 
                        BorderBrush="#dee2e6" BorderThickness="1" CornerRadius="3">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0" 
                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"
                                Margin="5"
                                BorderThickness="0"
                                Background="Transparent"
                                VerticalAlignment="Center">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>

                        <Button Grid.Column="1" Content="搜索" 
                               Style="{StaticResource ActionButtonStyle}"
                               Background="#3498db" Foreground="White"
                               Command="{Binding SearchCommand}"/>

                        <Button Grid.Column="2" Content="刷新" 
                               Style="{StaticResource ActionButtonStyle}"
                               Background="#95a5a6" Foreground="White"
                               Command="{Binding RefreshCommand}"/>
                    </Grid>
                </Border>

                <!-- 课程列表 -->
                <DataGrid Grid.Row="2" 
                         ItemsSource="{Binding Courses}"
                         SelectedItem="{Binding SelectedCourse}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         CanUserReorderColumns="False"
                         CanUserResizeRows="False"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         Margin="10">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="课程名称" Binding="{Binding Name}" Width="120"/>
                        <DataGridTextColumn Header="时长(分钟)" Binding="{Binding DurationMinutes}" Width="80"/>
                        <DataGridTextColumn Header="价格(元)" Binding="{Binding Price}" Width="80"/>
                        <DataGridCheckBoxColumn Header="双人课程" Binding="{Binding SupportsPairClass}" Width="80"/>
                        <DataGridTextColumn Header="描述" Binding="{Binding Description}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 操作按钮 -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" 
                           HorizontalAlignment="Center" Margin="10">
                    <Button Content="添加课程" 
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#27ae60" Foreground="White"
                           Command="{Binding AddCourseCommand}"/>
                    <Button Content="编辑课程" 
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#f39c12" Foreground="White"
                           Command="{Binding EditCourseCommand}"/>
                    <Button Content="删除课程" 
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#e74c3c" Foreground="White"
                           Command="{Binding DeleteCourseCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 右侧：课程编辑 -->
        <Border Grid.Column="1" Background="White" Margin="5" 
                BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
            <Border.Visibility>
                <Binding Path="IsEditing">
                    <Binding.Converter>
                        <BooleanToVisibilityConverter/>
                    </Binding.Converter>
                </Binding>
            </Border.Visibility>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 编辑标题 -->
                <TextBlock Grid.Row="0" 
                          Text="{Binding EditingCourse.Id, Converter={x:Static converters:IdToTitleConverter.Instance}}"
                          FontSize="16" FontWeight="Bold" 
                          Foreground="#2c3e50" Margin="15,15,15,10"/>

                <!-- 编辑表单 -->
                <ScrollViewer Grid.Row="1" Margin="15,0">
                    <StackPanel>
                        <TextBlock Text="课程名称 *" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingCourse.Name, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"/>

                        <TextBlock Text="课程描述" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingCourse.Description, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"
                                Height="60" TextWrapping="Wrap" 
                                AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>

                        <TextBlock Text="时长(分钟) *" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingCourse.DurationMinutes, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"/>

                        <TextBlock Text="价格(元)" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingCourse.Price, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"/>

                        <TextBlock Text="课程颜色" Style="{StaticResource LabelStyle}"/>
                        <ComboBox Margin="5" Padding="8" FontSize="12"
                                 SelectedValue="{Binding EditingCourse.Color}"
                                 SelectedValuePath="Tag">
                            <ComboBoxItem Content="蓝色" Tag="#3498db"/>
                            <ComboBoxItem Content="绿色" Tag="#2ecc71"/>
                            <ComboBoxItem Content="红色" Tag="#e74c3c"/>
                            <ComboBoxItem Content="橙色" Tag="#f39c12"/>
                            <ComboBoxItem Content="紫色" Tag="#9b59b6"/>
                            <ComboBoxItem Content="青色" Tag="#1abc9c"/>
                        </ComboBox>

                        <CheckBox Content="支持双人课程" 
                                 IsChecked="{Binding EditingCourse.SupportsPairClass}"
                                 Margin="5" FontSize="12"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- 保存/取消按钮 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" 
                           HorizontalAlignment="Center" Margin="15">
                    <Button Content="保存" 
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#27ae60" Foreground="White"
                           Command="{Binding SaveCourseCommand}"/>
                    <Button Content="取消" 
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#95a5a6" Foreground="White"
                           Command="{Binding CancelEditCommand}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
