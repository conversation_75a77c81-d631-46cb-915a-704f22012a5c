using Microsoft.EntityFrameworkCore;
using AutoClassTime.Data;
using AutoClassTime.Models;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace AutoClassTime.ViewModels
{
    /// <summary>
    /// 学生管理ViewModel
    /// </summary>
    public class StudentManagementViewModel : ViewModelBase
    {
        private readonly ClassTimeDbContext _context;
        private ObservableCollection<Student> _students;
        private Student _selectedStudent;
        private Student _editingStudent;
        private string _searchText;
        private bool _isEditing;

        public StudentManagementViewModel(ClassTimeDbContext context)
        {
            _context = context;
            Students = new ObservableCollection<Student>();

            // 初始化命令
            AddStudentCommand = new RelayCommand(AddStudent);
            EditStudentCommand = new RelayCommand(EditStudent, () => SelectedStudent != null);
            DeleteStudentCommand = new RelayCommand(async () => await DeleteStudentAsync(), () => SelectedStudent != null);
            SaveStudentCommand = new RelayCommand(async () => await SaveStudentAsync(), () => IsEditing);
            CancelEditCommand = new RelayCommand(CancelEdit, () => IsEditing);
            SearchCommand = new RelayCommand(async () => await SearchStudentsAsync());
            RefreshCommand = new RelayCommand(async () => await LoadStudentsAsync());

            // 加载学生数据
            _ = LoadStudentsAsync();
        }

        #region 属性

        /// <summary>
        /// 学生列表
        /// </summary>
        public ObservableCollection<Student> Students
        {
            get => _students;
            set => SetProperty(ref _students, value);
        }

        /// <summary>
        /// 选中的学生
        /// </summary>
        public Student SelectedStudent
        {
            get => _selectedStudent;
            set
            {
                SetProperty(ref _selectedStudent, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        /// <summary>
        /// 正在编辑的学生
        /// </summary>
        public Student EditingStudent
        {
            get => _editingStudent;
            set => SetProperty(ref _editingStudent, value);
        }

        /// <summary>
        /// 搜索文本
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        /// <summary>
        /// 是否正在编辑
        /// </summary>
        public bool IsEditing
        {
            get => _isEditing;
            set
            {
                SetProperty(ref _isEditing, value);
                CommandManager.InvalidateRequerySuggested();
            }
        }

        #endregion

        #region 命令

        public ICommand AddStudentCommand { get; }
        public ICommand EditStudentCommand { get; }
        public ICommand DeleteStudentCommand { get; }
        public ICommand SaveStudentCommand { get; }
        public ICommand CancelEditCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }

        #endregion

        #region 方法

        /// <summary>
        /// 加载学生数据
        /// </summary>
        private async Task LoadStudentsAsync()
        {
            try
            {
                var students = await _context.Students
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.Name)
                    .ToListAsync();

                Students.Clear();
                foreach (var student in students)
                {
                    Students.Add(student);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载学生数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 搜索学生
        /// </summary>
        private async Task SearchStudentsAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    await LoadStudentsAsync();
                    return;
                }

                var students = await _context.Students
                    .Where(s => s.IsActive &&
                               (s.Name.Contains(SearchText) ||
                                s.Phone.Contains(SearchText) ||
                                s.Email.Contains(SearchText)))
                    .OrderBy(s => s.Name)
                    .ToListAsync();

                Students.Clear();
                foreach (var student in students)
                {
                    Students.Add(student);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索学生失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 添加学生
        /// </summary>
        private void AddStudent()
        {
            EditingStudent = new Student();
            IsEditing = true;
        }

        /// <summary>
        /// 编辑学生
        /// </summary>
        private void EditStudent()
        {
            if (SelectedStudent == null) return;

            EditingStudent = new Student
            {
                Id = SelectedStudent.Id,
                Name = SelectedStudent.Name,
                Phone = SelectedStudent.Phone,
                Email = SelectedStudent.Email,
                Address = SelectedStudent.Address,
                Notes = SelectedStudent.Notes,
                IsActive = SelectedStudent.IsActive,
                CreatedAt = SelectedStudent.CreatedAt,
                UpdatedAt = SelectedStudent.UpdatedAt
            };
            IsEditing = true;
        }

        /// <summary>
        /// 保存学生
        /// </summary>
        private async Task SaveStudentAsync()
        {
            try
            {
                if (EditingStudent == null) return;

                // 验证必填字段
                if (string.IsNullOrWhiteSpace(EditingStudent.Name))
                {
                    MessageBox.Show("学生姓名不能为空", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (EditingStudent.Id == 0)
                {
                    // 新增学生
                    EditingStudent.CreatedAt = DateTime.Now;
                    EditingStudent.UpdatedAt = DateTime.Now;
                    _context.Students.Add(EditingStudent);
                }
                else
                {
                    // 更新学生
                    var existingStudent = await _context.Students.FindAsync(EditingStudent.Id);
                    if (existingStudent != null)
                    {
                        existingStudent.Name = EditingStudent.Name;
                        existingStudent.Phone = EditingStudent.Phone;
                        existingStudent.Email = EditingStudent.Email;
                        existingStudent.Address = EditingStudent.Address;
                        existingStudent.Notes = EditingStudent.Notes;
                        existingStudent.UpdatedAt = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();
                await LoadStudentsAsync();

                IsEditing = false;
                EditingStudent = null;

                MessageBox.Show("学生信息保存成功", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存学生信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消编辑
        /// </summary>
        private void CancelEdit()
        {
            IsEditing = false;
            EditingStudent = null;
        }

        /// <summary>
        /// 删除学生
        /// </summary>
        private async Task DeleteStudentAsync()
        {
            try
            {
                if (SelectedStudent == null) return;

                var result = MessageBox.Show($"确定要删除学生 '{SelectedStudent.Name}' 吗？\n注意：这将同时删除该学生的所有课程记录。",
                    "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                // 检查是否有未完成的课程
                var hasActiveClasses = await _context.ClassRecords
                    .AnyAsync(cr => (cr.StudentId == SelectedStudent.Id || cr.SecondStudentId == SelectedStudent.Id) &&
                                   cr.Status == ClassStatus.Scheduled);

                if (hasActiveClasses)
                {
                    var confirmResult = MessageBox.Show("该学生还有未完成的课程安排，确定要删除吗？",
                        "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                    if (confirmResult != MessageBoxResult.Yes) return;
                }

                // 软删除：设置IsActive为false
                SelectedStudent.IsActive = false;
                SelectedStudent.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();
                await LoadStudentsAsync();

                SelectedStudent = null;
                MessageBox.Show("学生删除成功", "删除成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除学生失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }
}
