<UserControl x:Class="AutoClassTime.Views.StudentManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:AutoClassTime.Converters"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderBrush" Value="#bdc3c7"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5,5,5,2"/>
            <Setter Property="Foreground" Value="#2c3e50"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧：学生列表 -->
        <Border Grid.Column="0" Background="White" Margin="5"
                BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题 -->
                <TextBlock Grid.Row="0" Text="学生管理"
                          FontSize="18" FontWeight="Bold"
                          Foreground="#2c3e50" Margin="15,15,15,10"/>

                <!-- 搜索栏 -->
                <Border Grid.Row="1" Background="#f8f9fa" Margin="10,0,10,10"
                        BorderBrush="#dee2e6" BorderThickness="1" CornerRadius="3">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0"
                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"
                                Margin="5"
                                BorderThickness="0"
                                Background="Transparent"
                                VerticalAlignment="Center">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>

                        <Button Grid.Column="1" Content="搜索"
                               Style="{StaticResource ActionButtonStyle}"
                               Background="#3498db" Foreground="White"
                               Command="{Binding SearchCommand}"/>

                        <Button Grid.Column="2" Content="刷新"
                               Style="{StaticResource ActionButtonStyle}"
                               Background="#95a5a6" Foreground="White"
                               Command="{Binding RefreshCommand}"/>
                    </Grid>
                </Border>

                <!-- 学生列表 -->
                <DataGrid Grid.Row="2"
                         ItemsSource="{Binding Students}"
                         SelectedItem="{Binding SelectedStudent}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         CanUserReorderColumns="False"
                         CanUserResizeRows="False"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         Margin="10">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="120"/>
                        <DataGridTextColumn Header="电话" Binding="{Binding Phone}" Width="120"/>
                        <DataGridTextColumn Header="邮箱" Binding="{Binding Email}" Width="150"/>
                        <DataGridTextColumn Header="地址" Binding="{Binding Address}" Width="*"/>
                        <DataGridTextColumn Header="创建时间" Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 操作按钮 -->
                <StackPanel Grid.Row="3" Orientation="Horizontal"
                           HorizontalAlignment="Center" Margin="10">
                    <Button Content="添加学生"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#27ae60" Foreground="White"
                           Command="{Binding AddStudentCommand}"/>
                    <Button Content="编辑学生"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#f39c12" Foreground="White"
                           Command="{Binding EditStudentCommand}"/>
                    <Button Content="删除学生"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#e74c3c" Foreground="White"
                           Command="{Binding DeleteStudentCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 右侧：学生编辑 -->
        <Border Grid.Column="1" Background="White" Margin="5"
                BorderBrush="#bdc3c7" BorderThickness="1" CornerRadius="5">
            <Border.Visibility>
                <Binding Path="IsEditing">
                    <Binding.Converter>
                        <BooleanToVisibilityConverter/>
                    </Binding.Converter>
                </Binding>
            </Border.Visibility>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 编辑标题 -->
                <TextBlock Grid.Row="0"
                          Text="{Binding EditingStudent.Id, Converter={x:Static converters:IdToTitleConverter.Instance}}"
                          FontSize="16" FontWeight="Bold"
                          Foreground="#2c3e50" Margin="15,15,15,10"/>

                <!-- 编辑表单 -->
                <ScrollViewer Grid.Row="1" Margin="15,0">
                    <StackPanel>
                        <TextBlock Text="姓名 *" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingStudent.Name, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"/>

                        <TextBlock Text="电话" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingStudent.Phone, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"/>

                        <TextBlock Text="邮箱" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingStudent.Email, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"/>

                        <TextBlock Text="地址" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingStudent.Address, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"/>

                        <TextBlock Text="备注" Style="{StaticResource LabelStyle}"/>
                        <TextBox Text="{Binding EditingStudent.Notes, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource InputTextBoxStyle}"
                                Height="80" TextWrapping="Wrap"
                                AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- 保存/取消按钮 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal"
                           HorizontalAlignment="Center" Margin="15">
                    <Button Content="保存"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#27ae60" Foreground="White"
                           Command="{Binding SaveStudentCommand}"/>
                    <Button Content="取消"
                           Style="{StaticResource ActionButtonStyle}"
                           Background="#95a5a6" Foreground="White"
                           Command="{Binding CancelEditCommand}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
