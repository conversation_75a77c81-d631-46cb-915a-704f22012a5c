using Microsoft.EntityFrameworkCore;
using AutoClassTime.Data;
using System;
using System.Threading.Tasks;
using System.IO;
using System.Linq;

namespace AutoClassTime.Services
{
    /// <summary>
    /// 数据库服务类
    /// </summary>
    public class DatabaseService
    {
        private readonly ClassTimeDbContext _context;

        public DatabaseService()
        {
            _context = new ClassTimeDbContext();
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        public async Task InitializeDatabaseAsync()
        {
            try
            {
                // 检查是否需要重新创建数据库（解决TimeSpan排序问题）
                var dbPath = GetDatabasePath();
                if (File.Exists(dbPath))
                {
                    try
                    {
                        // 尝试查询一个简单的操作来测试数据库兼容性
                        await _context.Students.CountAsync();
                    }
                    catch
                    {
                        // 如果出现错误，删除旧数据库重新创建
                        Console.WriteLine("检测到数据库兼容性问题，正在重新创建数据库...");
                        File.Delete(dbPath);
                    }
                }

                // 确保数据库被创建
                await _context.Database.EnsureCreatedAsync();

                Console.WriteLine("数据库初始化成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 备份数据库
        /// </summary>
        public async Task<bool> BackupDatabaseAsync(string backupPath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(backupPath))
                {
                    string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                    string appFolder = Path.Combine(appDataPath, "AutoClassTime", "Backups");

                    if (!Directory.Exists(appFolder))
                    {
                        Directory.CreateDirectory(appFolder);
                    }

                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    backupPath = Path.Combine(appFolder, $"ClassTime_Backup_{timestamp}.db");
                }

                string sourcePath = GetDatabasePath();

                if (File.Exists(sourcePath))
                {
                    File.Copy(sourcePath, backupPath, true);
                    Console.WriteLine($"数据库备份成功: {backupPath}");
                    return true;
                }
                else
                {
                    Console.WriteLine("源数据库文件不存在");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库备份失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 恢复数据库
        /// </summary>
        public async Task<bool> RestoreDatabaseAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    Console.WriteLine("备份文件不存在");
                    return false;
                }

                string targetPath = GetDatabasePath();

                // 关闭数据库连接
                await _context.Database.CloseConnectionAsync();

                // 复制备份文件
                File.Copy(backupPath, targetPath, true);

                Console.WriteLine($"数据库恢复成功: {backupPath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库恢复失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取数据库文件路径
        /// </summary>
        private string GetDatabasePath()
        {
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            string appFolder = Path.Combine(appDataPath, "AutoClassTime");
            return Path.Combine(appFolder, "ClassTime.db");
        }

        /// <summary>
        /// 检查数据库连接
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                await _context.Database.OpenConnectionAsync();
                await _context.Database.CloseConnectionAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库连接测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取数据库统计信息
        /// </summary>
        public async Task<DatabaseStats> GetDatabaseStatsAsync()
        {
            try
            {
                var stats = new DatabaseStats
                {
                    StudentCount = await _context.Students.CountAsync(s => s.IsActive),
                    CourseCount = await _context.Courses.CountAsync(c => c.IsActive),
                    TimeSlotCount = await _context.TimeSlots.CountAsync(),
                    ClassRecordCount = await _context.ClassRecords.CountAsync(),
                    DatabaseSize = GetDatabaseSize()
                };

                return stats;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取数据库统计信息失败: {ex.Message}");
                return new DatabaseStats();
            }
        }

        /// <summary>
        /// 获取数据库文件大小
        /// </summary>
        private long GetDatabaseSize()
        {
            try
            {
                string dbPath = GetDatabasePath();
                if (File.Exists(dbPath))
                {
                    return new FileInfo(dbPath).Length;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// 数据库统计信息
    /// </summary>
    public class DatabaseStats
    {
        public int StudentCount { get; set; }
        public int CourseCount { get; set; }
        public int TimeSlotCount { get; set; }
        public int ClassRecordCount { get; set; }
        public long DatabaseSize { get; set; }

        public string DatabaseSizeFormatted => FormatBytes(DatabaseSize);

        private string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
