# 智能文本解析排课功能说明

## 🎯 功能概述

智能文本解析排课是私教排课系统的核心功能，能够解析您输入的复杂排课文本，自动识别老师时间、学生需求、约束条件，并生成最优的排课方案。

## ✨ 功能特点

### 1. 智能文本解析
- **多格式支持**：支持自然语言描述的排课需求
- **自动识别**：智能识别科目、时间、学生姓名
- **约束理解**：理解时间限制和特殊要求

### 2. 自动排课算法
- **冲突检测**：自动避免时间冲突
- **约束满足**：遵守所有限制条件
- **最优分配**：智能选择最佳时间安排

### 3. 结果输出
- **按学生分组**：清晰展示每个学生的课程安排
- **时间排序**：按日期和时间有序显示
- **小班标识**：明确标注小班课程信息

## 📝 支持的文本格式

### 老师时间安排
```
语文老师：
5.1号
7-9起赫
9-11峻硕
11-13皓铮
```

### 学生课程需求
```
淇轩上2节数学，3节英语，2节物理，3节化学
皓铮上2节语文（有一节1.5h），3节英语，3节物理，3节化学，3节道法（和峻硕一起上课）
```

### 时间约束
```
皓铮5.3号8-17不可排课
淇轩18点以后不可排课
峻硕5.3 5.4 5.5号上午10-12不可排课
```

### 小班课程
```
英语老师
5.2
9-11皓铮、淇轩 
17-19峻硕、思涵
```

## 🚀 使用方法

### 第一步：输入文本
1. 点击顶部菜单的"智能排课"按钮
2. 在左侧输入框中粘贴或输入排课文本
3. 或点击"加载示例"查看标准格式

### 第二步：开始解析
1. 点击"🚀 开始智能排课"按钮
2. 系统自动解析文本内容
3. 等待处理完成（通常几秒钟）

### 第三步：查看结果
1. 右侧显示按学生分组的排课结果
2. 包含日期、时间、科目、小班信息
3. 如有冲突会显示警告信息

## 📋 文本格式规范

### 科目标识
- 支持：语文、数学、英语、物理、化学、道法
- 格式：`科目名称老师：` 或直接 `科目名称`

### 日期格式
- 标准格式：`5.1号` 或 `5.1`
- 支持连续日期：`5.1-5.5`

### 时间格式
- 整点：`7-9`、`18-20`
- 半点：`11-12.30`、`20.30-22.30`
- 分钟：`13.45-15.45`

### 学生姓名
- 支持中文姓名：起赫、峻硕、皓铮、淇轩、思涵、许铜、欣怡
- 小班课程：`皓铮、淇轩`（用顿号分隔）

### 课程需求
- 格式：`学生名上X节科目`
- 示例：`淇轩上2节数学，3节英语`
- 特殊要求：`皓铮上2节语文（有一节1.5h）`

### 约束条件
- 不可排课：`学生名X.X号X-X不可排课`
- 可排课：`学生名X.X号X点以后可排课`

## 🎯 解析规则

### 自动识别
1. **科目识别**：根据关键词自动识别科目类型
2. **时间解析**：智能解析各种时间格式
3. **学生匹配**：自动匹配学生姓名
4. **约束提取**：提取时间限制条件

### 排课算法
1. **需求分析**：统计每个学生的课程需求
2. **时间匹配**：将需求与可用时间匹配
3. **冲突检测**：避免时间冲突和同日同科
4. **最优选择**：优先安排小班课程

### 约束处理
1. **时间限制**：严格遵守不可排课时间
2. **同日限制**：同一学生同一科目不在同一天
3. **时长要求**：确保课程时长符合要求
4. **小班要求**：优先满足指定的小班安排

## ⚠️ 注意事项

### 文本要求
- 使用中文标点符号
- 保持格式一致性
- 学生姓名要准确
- 时间格式要规范

### 约束条件
- 没有给出的时间不可以安排课
- 同一个学生同一科课不可以安排在一天
- 所有课程与课程间时间不可以重叠
- 每节课除特殊说明外都是2小时

### 处理限制
- 如果无法满足所有需求，会显示警告
- 冲突的安排会被跳过
- 建议检查输入文本的完整性

## 📊 输出格式

### 标准输出
```
=== 自动排课结果 ===

【淇轩】
  5.1:
    7-9 化学
  5.2:
    9-11 英语（与皓铮一起）
    14-16 数学
  ...

【皓铮】
  5.1:
    9-11 语文
    11-12.30 语文
  ...
```

### 警告信息
- 无法安排的课程会显示具体原因
- 时间冲突会给出详细说明
- 约束违反会提供解决建议

## 🔧 高级技巧

### 优化输入
1. **分块输入**：按科目分块组织文本
2. **清晰标注**：明确标注特殊要求
3. **完整信息**：提供完整的时间和需求信息

### 结果验证
1. **检查总数**：验证课程总数是否正确
2. **时间核对**：确认时间安排合理
3. **约束验证**：检查是否违反约束条件

### 问题排查
1. **解析失败**：检查文本格式是否正确
2. **排课不全**：检查时间资源是否充足
3. **冲突警告**：调整时间安排或需求

## 💡 使用建议

1. **首次使用**：建议先用示例文本熟悉功能
2. **复杂需求**：可以分批次处理复杂的排课需求
3. **结果保存**：重要的排课结果建议复制保存
4. **持续优化**：根据实际需要调整输入格式

---

**智能文本解析排课让复杂的排课工作变得简单高效！** 🎉
